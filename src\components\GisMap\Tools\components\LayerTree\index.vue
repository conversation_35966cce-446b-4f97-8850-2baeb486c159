<template>
  <div class="laytree-container">
    <div class="title-header">图层</div>
    <div class="tree-box">
      <el-tree
        ref="refCheckdata"
        :data="treeData"
        show-checkbox
        node-key="id"
        :expand-on-click-node="expandNode"
        :render-after-expand="false"
        check-on-click-node
        :props="defaultProps"
        @check-change="onNodeClick"
      >
        <template #default="{ node }">
          <!-- <svg-icon v-if="data.icon !== 'default'" :icon="data.icon" /> -->
          <span class="label">
            {{ node.label }}
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup>
import { defaultCheckedLayers } from "@/hooks/gishooks";
import { layerTreeListMap } from "./config";
import { mapStates } from "@/components/GisMap/mapStates";
import { useRoute } from "vue-router";
import {computed, ref, watch} from "vue";
import {ElTree} from "element-plus";
import {bridgeModelData, pipeModelData} from "@/components/GisMap/common/gisInfo.js";
const route = useRoute();
const expandNode = ref(false);
const refCheckdata = ref();
const emits = defineEmits(['onClose','checkLayer'])

const defaultProps = ref({
  children: "children",
  label: "label",
});


//default-expand-all
const treeData = computed(() => {
  return layerTreeListMap?.[route.path] ?? [];
});

const onClose = () => {
  emits("onClose");
};

const lineIds = [
/*  [["supplyWater_Pipeline"], ["supplyWater_PipeRisk"]],
  [
    [
      "supplyWater_RiskLevel",
      "riskLevel_Large",
      "riskLevel_Middle",
      "riskLevel_Normal",
      "riskLevel_Low",
    ],
    [
      "riskControl_NotCompleted",
      "riskControl_Completed",
      "supplyWater_RiskControl",
    ],
  ],*/
];

const showSpecialModel = (id, isChecked) => {
    if (pipeModelData[id]) {
        mapStates.earth.basemap.showModelLayerByNames(pipeModelData[id], isChecked) //显示模型管道
    } else if (bridgeModelData[id]) {
        mapStates.earth.basemap.showModelLayerByNames(bridgeModelData[id], isChecked) //桥梁模型
    }
}

const onNodeClick = (node, isChecked) => {
  if (refCheckdata.value) {
    const checkedIds = refCheckdata.value?.getCheckedKeys();
    lineIds.forEach((line) => {
      if (
        line[0].includes(node.id) &&
        isChecked &&
        checkedIds.filter((v) => line[1].includes(v)).length > 0
      ) {
        line[1].forEach((id) => {
          refCheckdata.value?.setChecked(id, false, false);
        });
      } else if (
        line[1].includes(node.id) &&
        isChecked &&
        checkedIds.filter((v) => line[0].includes(v)).length > 0
      ) {
        line[0].forEach((id) => {
          refCheckdata.value?.setChecked(id, false, false);
        });
      }
    });
    defaultCheckedLayers.value = refCheckdata.value?.getCheckedKeys();
  }
  if (!node.children) {
   //todo 切换图层显示隐藏
    mapStates.earth.entity.toggleLayerVisibleById(node.id, isChecked);
    if (!isChecked) {
        //管道模型隐藏
        showSpecialModel(node.id, isChecked);
    }
    emits("checkLayer", node.id, isChecked);
  }
  if (node.id && !isChecked) {
    // 如果是点
   /* if (
      supStates.hcmap?.originalLayer &&
      supStates.hcmap?.originalLayer.get("layerId") === node.id
    ) {
      supStates.popup.clearAllPopups();
      supStates.hcmap.removeHighlightFeature();
    } else if (supStates.hcmap.hightLightPolylineOriginaLayerId === node.id) {
      supStates.popup.clearAllPopups();
      supStates.hcmap.removeLayerById("locatePolylineLayer");
    }*/
  }
};

watch(
  () => defaultCheckedLayers.value,
  (ck) => {
      window. setTimeout(() => {
          refCheckdata.value &&
          refCheckdata.value?.setCheckedKeys(defaultCheckedLayers.value);
      }, 100);
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.laytree-container {
  width: 182px;
  height: 388px;
  background: rgba(13, 37, 82, 0.8);
  border-radius: 4px;
  border: 1px solid #182d54;
  padding: 15px 8px 15px 12px;
  display: flex;
  flex-direction: column;
  .title-header {
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    padding-bottom: 12px;
  }

  .tree-box {
    flex: 1;
    overflow: auto;
  }

  :deep(.el-tree) {
    position: relative;
    cursor: default;
    background: none;
    --el-tree-expand-icon-color: #fff;
    .el-tree-node__expand-icon {
      color: #cae1f8;
      font-size: 12px;
    }
    .el-checkbox {
      --el-checkbox-checked-icon-color: rgba(18, 34, 54, 0.8) !important;
    }
    .el-tree-node {
      .label {
        color: #cae1f8;
      }
    }
    .is-checked {
      .label {
        color: #cae1f8 !important;
      }
    }
    .el-tree__empty-text {
      font-size: 14px;
    }
    .label {
      padding-left: 5px;
      font-size: 12px;
      font-weight: 400;
    }
    .el-tree-node__content {
      height: 26px;
      &:hover {
        background-color: unset !important;
      }
      & > label.el-checkbox {
        margin-right: 0px;
      }
    }

    .el-tree-node:focus > .el-tree-node__content {
      background-color: unset !important;
    }
    .el-checkbox__inner {
      height: 12px !important;
      width: 12px !important;
      background-color: transparent !important;
      border: 1px solid #cae1f8 !important;
      &:after {
        top: 0px;
        height: 7px;
        width: 3px;
        left: 3px;
      }
      &::before {
        top: 4px;
      }
    }
    .el-checkbox__input {
      &.is-checked .el-checkbox__inner {
        background: #fff !important;
      }
      &.is-indeterminate .el-checkbox__inner {
        background: #fff !important;
      }
    }
  }
}
</style>
