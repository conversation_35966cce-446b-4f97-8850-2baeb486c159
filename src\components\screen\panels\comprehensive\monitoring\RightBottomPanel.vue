<template>
  <PanelBox title="报警处置统计" class="right-bottom-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-color" style="background: #1B50FF;"></span>
          <span class="legend-text">未处置</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #FFD512;"></span>
          <span class="legend-text">处置中</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: #41C4A3;"></span>
          <span class="legend-text">已处置</span>
        </div>
      </div>
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'
import moment from 'moment'
import { getAlarmHandleStatistics } from '@/api/comprehensive'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 图表实例
const chartRef = ref(null)
let chartInstance = null

// 加载状态
const loading = ref(false)



// 当前数据
const currentData = ref({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '未处置', data: [0, 0, 0, 0] },
    { name: '处置中', data: [0, 0, 0, 0] },
    { name: '已处置', data: [0, 0, 0, 0] }
  ]
})

// 存储原始数据用于tooltip显示
const originalData = ref({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '未处置', data: [0, 0, 0, 0] },
    { name: '处置中', data: [0, 0, 0, 0] },
    { name: '已处置', data: [0, 0, 0, 0] }
  ]
})

// 获取真实数值的辅助函数
const getRealValue = (seriesName, categoryIndex) => {
  const seriesIndex = originalData.value.series.findIndex(s => s.name === seriesName)
  if (seriesIndex !== -1 && categoryIndex !== -1) {
    return originalData.value.series[seriesIndex].data[categoryIndex]
  }
  return 0
}

// 获取时间范围
const getTimeRange = (type) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime

  switch (type) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }

  return { startTime, endTime }
}

// 获取报警处置数据
const fetchAlarmHandleData = async () => {
  try {
    loading.value = true
    const { startTime, endTime } = getTimeRange(timeRange.value)

    const response = await getAlarmHandleStatistics({
      startTime,
      endTime
    })

    if (response.code === 200 && response.data) {
      processChartData(response.data)
    }
  } catch (error) {
    console.error('获取报警处置数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理图表数据
const processChartData = (data) => {
  const categories = ['燃气', '排水', '供热', '桥梁']
  const unhandledData = [0, 0, 0, 0]
  const handlingData = [0, 0, 0, 0]
  const handledData = [0, 0, 0, 0]

  // 业务类型映射
  const businessMap = {
    7000501: 0, // 燃气
    7000502: 1, // 排水
    7000503: 2, // 供热
    7000504: 3  // 桥梁
  }

  data.forEach(item => {
    const index = businessMap[item.relatedBusiness]
    if (index !== undefined) {
      unhandledData[index] = item.unhandledCount || 0
      handlingData[index] = item.handlingCount || 0
      handledData[index] = item.handledCount || 0
    }
  })

  // 保存原始数据
  originalData.value = {
    categories,
    series: [
      { name: '未处置', data: [...unhandledData] },
      { name: '处置中', data: [...handlingData] },
      { name: '已处置', data: [...handledData] }
    ]
  }

  // 处理显示数据（对数刻度需要将0值转换为小数）
  currentData.value = {
    categories,
    series: [
      { name: '未处置', data: unhandledData.map(val => val === 0 ? 0.1 : val) },
      { name: '处置中', data: handlingData.map(val => val === 0 ? 0.1 : val) },
      { name: '已处置', data: handledData.map(val => val === 0 ? 0.1 : val) }
    ]
  }

  updateChartData()
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁之前的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 确保容器已渲染完成并且有尺寸
  setTimeout(() => {
    chartInstance = echarts.init(chartRef.value)
    updateChartData()

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', resizeChart)
  }, 100)
}

// 更新图表数据
const updateChartData = () => {
  if (!chartInstance) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
      formatter: function (params) {
        const time = {
          'week': '本周',
          'month': '本月',
          'year': '本年'
        }[timeRange.value]

        const category = params[0].name
        let html = `<div style="color: #fff;">${time}${category}</div>`

        params.forEach((item, index) => {
          const color = {
            '未处置': '#1B50FF',
            '处置中': '#FFD512',
            '已处置': '#41C4A3'
          }[item.seriesName]

          // 获取真实数值（从原始数据中获取，而不是对数处理后的值）
          const categoryIndex = currentData.value.categories.indexOf(category)
          const realValue = getRealValue(item.seriesName, categoryIndex)
          const displayValue = realValue

          html += `<div style="display: flex; align-items: center; margin-top: 5px;">
                    <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; margin-right: 5px;"></span>
                    <span>${item.seriesName}: ${displayValue}个</span>
                  </div>`
        })

        return html
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '35%',
      top: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: currentData.value.categories,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400
      }
    },
    yAxis: {
      type: 'log',
      logBase: 10,
      name: '单位（个）',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        padding: [0, 0, 0, 0]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'PingFangSC, PingFang SC',
        fontWeight: 400,
        formatter: function(value) {
          // 对数刻度下的标签格式化
          if (value === 0) return '0'
          if (value < 1) return value.toFixed(1)
          if (value < 10) return Math.round(value)
          if (value < 100) return Math.round(value)
          if (value < 1000) return Math.round(value)
          return Math.round(value)
        }
      },
      min: function(value) {
        // 动态设置最小值，确保0值也能显示
        return value.min > 0 ? Math.max(0.1, value.min * 0.1) : 0.1
      },
      max: function(value) {
        // 动态设置最大值
        return value.max * 1.2
      }
    },
    series: [
      {
        name: '未处置',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(27, 80, 255, 0.9)' },
            { offset: 1, color: 'rgba(27, 80, 255, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[0].data
      },
      {
        name: '处置中',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 213, 18, 0.9)' },
            { offset: 1, color: 'rgba(255, 213, 18, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[1].data
      },
      {
        name: '已处置',
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(65, 196, 163, 0.9)' },
            { offset: 1, color: 'rgba(65, 196, 163, 0.1)' }
          ]),
          borderRadius: [2, 2, 0, 0]
        },
        data: currentData.value.series[2].data
      }
    ]
  }

  chartInstance.setOption(option)
}

// 响应窗口大小变化
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchAlarmHandleData()
}

// 生命周期钩子
onMounted(() => {
  // 使用setTimeout确保DOM已经渲染完毕
  setTimeout(() => {
    initChart()
    fetchAlarmHandleData()
  }, 300)
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style scoped>
.right-bottom-panel {
  height: 310px;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-legend {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-bottom: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-container {
  width: 100%;
  flex: 1;
  min-height: 250px;
}

.com-select {
  margin-right: 20px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }

  .chart-container {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 8px;
  }

  .chart-container {
    height: 300px;
  }
}

@media screen and (min-width: 2561px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 15px;
  }

  .chart-container {
    height: 380px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 15px;
  }

  .chart-container {
    height: 360px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .right-bottom-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }

  .chart-container {
    height: 320px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .right-bottom-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
  }

  .chart-container {
    height: 280px;
  }
}
</style>