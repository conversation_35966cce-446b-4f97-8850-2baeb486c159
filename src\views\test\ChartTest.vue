<template>
  <div class="chart-test-container">
    <h2>图表效果测试</h2>
    
    <!-- 原始数据展示 -->
    <div class="data-section">
      <h3>原始数据</h3>
      <div class="data-row">
        <span>已处置: {{ testData.handled }}</span>
        <span>处置中: {{ testData.handling }}</span>
        <span>未处置: {{ testData.unhandled }}</span>
        <span>总计: {{ testData.total }}</span>
      </div>
    </div>

    <!-- 测试不同数据场景 -->
    <div class="test-buttons">
      <button @click="setScenario1">场景1: 正常数据</button>
      <button @click="setScenario2">场景2: 极端差距</button>
      <button @click="setScenario3">场景3: 均匀分布</button>
    </div>

    <!-- 图表展示区域 -->
    <div class="chart-demo">
      <div class="chart-container">
        <div class="chart-wrapper">
          <div ref="chartRef" class="alarm-chart"></div>
        </div>
        <div class="legend-container">
          <div class="legend-item" v-for="item in legendData" :key="item.name">
            <span class="legend-dot" :style="{ backgroundColor: item.color }"></span>
            <span class="legend-label">{{ item.name }}</span>
            <span class="legend-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表相关
const chartRef = ref(null)
let chartInstance = null

// 测试数据
const testData = reactive({
  total: 1929,
  handled: 1928,
  handling: 1,
  unhandled: 35
})

// 图例数据
const legendData = computed(() => [
  { name: '已处置', value: testData.handled, color: '#3FD87C' },
  { name: '处置中', value: testData.handling, color: '#FFC75A' },
  { name: '未处置', value: testData.unhandled, color: '#FF6D28' }
])

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#3AA1FF',
      borderWidth: 1,
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    legend: {
      show: false
    },
    series: [
      {
        name: '报警统计',
        type: 'pie',
        radius: ['45%', '70%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderColor: '#1a1a1a',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
            color: '#FFFFFF'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: testData.handled, name: '已处置', itemStyle: { color: '#3FD87C' } },
          { value: testData.handling, name: '处置中', itemStyle: { color: '#FFC75A' } },
          { value: testData.unhandled, name: '未处置', itemStyle: { color: '#FF6D28' } }
        ]
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const option = {
    series: [
      {
        data: [
          { value: testData.handled, name: '已处置', itemStyle: { color: '#3FD87C' } },
          { value: testData.handling, name: '处置中', itemStyle: { color: '#FFC75A' } },
          { value: testData.unhandled, name: '未处置', itemStyle: { color: '#FF6D28' } }
        ]
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 测试场景
const setScenario1 = async () => {
  testData.handled = 270
  testData.handling = 90
  testData.unhandled = 20
  testData.total = 380
  await nextTick()
  updateChart()
}

const setScenario2 = async () => {
  testData.handled = 1928
  testData.handling = 1
  testData.unhandled = 35
  testData.total = 1964
  await nextTick()
  updateChart()
}

const setScenario3 = async () => {
  testData.handled = 100
  testData.handling = 100
  testData.unhandled = 100
  testData.total = 300
  await nextTick()
  updateChart()
}

onMounted(async () => {
  await nextTick()
  initChart()
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.chart-test-container {
  padding: 20px;
  background: #1a1a1a;
  color: #fff;
  min-height: 100vh;
}

.data-section {
  margin-bottom: 20px;
}

.data-row {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.test-buttons {
  margin-bottom: 30px;
  display: flex;
  gap: 10px;
}

.test-buttons button {
  padding: 8px 16px;
  background: #3AA1FF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-buttons button:hover {
  background: #66B8FF;
}

.chart-demo {
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-radius: 8px;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 0 10px;
}

.chart-wrapper {
  flex: 0 0 200px;
  height: 200px;
}

.alarm-chart {
  width: 100%;
  height: 100%;
}

.legend-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
}

.legend-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  min-width: 50px;
  text-align: right;
}
</style>
