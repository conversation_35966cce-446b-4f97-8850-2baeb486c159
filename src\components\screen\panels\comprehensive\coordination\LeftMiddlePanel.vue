<template>
  <PanelBox title="预警级别统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-header">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-icon total"></span>
            <span class="legend-text">一级预警</span>
          </div>
          <div class="legend-item">
            <span class="legend-icon deployed"></span>
            <span class="legend-text">二级预警</span>
          </div>
          <div class="legend-item">
            <span class="legend-icon level3"></span>
            <span class="legend-text">三级预警</span>
          </div>
        </div>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import * as echarts from 'echarts'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getLinkageWarningLevelStatistics } from '@/api/comprehensive'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 页面可见性状态
let isPageVisible = true
// 定时器ID
let refreshTimer = null

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 加载状态
const loading = ref(false)



// 图表数据
const chartData = reactive({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '一级预警', values: [0, 0, 0, 0] },
    { name: '二级预警', values: [0, 0, 0, 0] },
    { name: '三级预警', values: [0, 0, 0, 0] }
  ]
})

/**
 * 根据时间范围获取开始和结束时间
 * @param {string} range 时间范围 ('week', 'month', 'year')
 * @returns {object} 包含startTime和endTime的对象
 */
const getTimeRange = (range) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime = ''
  
  switch (range) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }
  
  return { startTime, endTime }
}

/**
 * 获取联动预警级别统计数据
 */
const fetchWarningLevelData = async () => {
  loading.value = true
  try {
    const { startTime, endTime } = getTimeRange(timeRange.value)
    const response = await getLinkageWarningLevelStatistics({
      startTime,
      endTime
    })
    
    if (response.code === 200 && response.data) {
      processChartData(response.data)
    }
  } catch (error) {
    console.error('获取联动预警级别统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理图表数据
 * @param {object} data API返回的数据
 */
const processChartData = (data) => {
  // 业务类型映射
  const businessMap = {
    7000501: '燃气',
    7000502: '排水', 
    7000503: '供热',
    7000504: '桥梁'
  }
  
  // 初始化数据
  const categories = ['燃气', '排水', '供热', '桥梁']
  const level1Values = [0, 0, 0, 0]
  const level2Values = [0, 0, 0, 0]
  const level3Values = [0, 0, 0, 0]
  
  // 处理统计数据
  if (data.statistics && Array.isArray(data.statistics)) {
    data.statistics.forEach(item => {
      const businessName = businessMap[item.relatedBusiness]
      const index = categories.indexOf(businessName)
      
      if (index !== -1) {
        level1Values[index] = item.level1Count || 0
        level2Values[index] = item.level2Count || 0
        level3Values[index] = item.level3Count || 0
      }
    })
  }
  
  // 更新图表数据
  chartData.categories = categories
  chartData.series = [
    { name: '一级预警', values: level1Values },
    { name: '二级预警', values: level2Values },
    { name: '三级预警', values: level3Values }
  ]
  
  // 更新图表
  updateChart()
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 获取新的数据
  fetchWarningLevelData()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const option = createChartOption()
  chartInstance.setOption(option)
}

// 创建图表配置
const createChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: '17%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
      formatter: function(params) {
        const categoryName = params[0].name;
        let html = `<div style="font-weight:bold;margin-bottom:8px;color:#3CF3FF;">${categoryName}</div>`;
        
        params.forEach(item => {
          let color = '';
          let unit = '个';
          
          if (item.seriesName === '一级预警') {
            color = '#FF2929';
          } else if (item.seriesName === '二级预警') {
            color = '#FF8A14';
          } else if (item.seriesName === '三级预警') {
            color = '#FFFA00';
          }
          
          html += `<div style="display:flex;justify-content:space-between;margin:5px 0;align-items:center;">
            <span style="margin-right:15px;display:inline-block;">
              <span style="display:inline-block;width:8px;height:8px;border-radius:4px;background-color:${color};margin-right:5px;"></span>
              ${item.seriesName}:
            </span>
            <span style="font-weight:bold;color:${color};">${item.value}${unit}</span>
          </div>`;
        });
        
        return html;
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(5, 122, 255, 0.1)'
        }
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 16,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '单位(个)',
      nameTextStyle: {
        color: '#FFFFFF',
        fontSize: 12,
        padding: [0, -18, 0, 0]
      },
      max: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '一级预警',
        type: 'bar',
        data: chartData.series[0].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFA7A7' },
              { offset: 1, color: '#FF0000' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#FF0000',
          borderWidth: 1,
          shadowColor: 'rgba(5, 122, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(5, 90, 219, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(5, 90, 219, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '二级预警',
        type: 'bar',
        data: chartData.series[1].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFB18F' },
              { offset: 1, color: '#FF3D00' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#FF3D00',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      },
      {
        name: '三级预警',
        type: 'bar',
        data: chartData.series[1].values,
        barWidth: 19,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#FFFFFF' },
              { offset: 1, color: '#FFFA00' }
            ]
          },
          borderRadius: [2, 2, 0, 0],
          borderColor: '#FFFA00',
          borderWidth: 1,
          shadowColor: 'rgba(35, 202, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetX: 3,
          shadowOffsetY: 3
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(35, 202, 255, 0.7)'
          }
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(35, 202, 255, 0.2)',
          borderRadius: [0, 0, 0, 0]
        }
      }
    ]
  };
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  const option = createChartOption()
  chartInstance.setOption(option)
  
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (document.hidden) {
    isPageVisible = false
    // 页面不可见时清除定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  } else {
    isPageVisible = true
    // 页面可见时重新启动定时器
    startDataRefresh()
  }
}

// 启动数据刷新
const startDataRefresh = () => {
  if (!refreshTimer && isPageVisible) {
    const refreshInterval = 600000 // 10分钟刷新一次
    refreshTimer = setInterval(() => {
      // 刷新数据
      fetchWarningLevelData()
    }, refreshInterval)
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  fetchWarningLevelData()
  // 启动数据刷新
  startDataRefresh()
  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  // 清理事件监听和定时器
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #FF2929;
}

.legend-icon.deployed {
  background: #FF8A14;
}
.legend-icon.level3 {
  background: #FFFA00;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 180px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .chart-wrapper {
    min-height: 170px;
  }
}
</style>