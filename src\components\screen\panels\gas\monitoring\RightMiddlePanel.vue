<template>
  <PanelBox title="报警趋势分析" class="gas-monitoring-right-middle-panel">
    <div class="panel-content">

      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon total"></span>
          <span class="legend-text">总数</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-one"></span>
          <span class="legend-text">一级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-two"></span>
          <span class="legend-text">二级报警</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon level-three"></span>
          <span class="legend-text">三级报警</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getMonitorAnalysisTrendStatistics } from '@/api/gas'

const chartRef = ref(null)
let chartInstance = null

// 预设颜色，与设计稿保持一致
const colors = {
  total: '#055ADB',
  levelOne: '#FF311D',
  levelTwo: '#FF6817',
  levelThree: '#FFD32E'
}

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '总数',
      data: [],
      color: colors.total
    },
    {
      name: '一级报警',
      data: [],
      color: colors.levelOne
    },
    {
      name: '二级报警',
      data: [],
      color: colors.levelTwo
    },
    {
      name: '三级报警',
      data: [],
      color: colors.levelThree
    }
  ]
})

// 生成测试数据
const generateMockData = () => {
  // 生成当月31天的日期
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1
  const daysInMonth = 31

  const xAxisData = []
  const totalData = []
  const levelOneData = []
  const levelTwoData = []
  const levelThreeData = []

  // 基础数值，用于生成随机数据
  const baseValue = {
    total: 50,
    levelOne: 30,
    levelTwo: 40,
    levelThree: 35
  }

  for (let i = 1; i <= daysInMonth; i++) {
    const day = i < 10 ? `0${i}` : i
    xAxisData.push(`${currentMonth}.${day}`)

    // 生成随机值，模拟波动
    const random = (min, max) => Math.floor(Math.random() * (max - min + 1) + min)

    // 模拟三级报警数据
    const levelThreeValue = random(baseValue.levelThree - 25, baseValue.levelThree + 45)
    levelThreeData.push(levelThreeValue)

    // 模拟二级报警数据
    const levelTwoValue = random(baseValue.levelTwo - 15, baseValue.levelTwo + 30)
    levelTwoData.push(levelTwoValue)

    // 模拟一级报警数据
    const levelOneValue = random(baseValue.levelOne - 15, baseValue.levelOne + 35)
    levelOneData.push(levelOneValue)

    // 总数略高于所有报警之和
    const totalValue = levelOneValue + levelTwoValue + random(0, 10)
    totalData.push(totalValue)
  }

  // 更新图表数据
  chartData.value.xAxis = xAxisData
  chartData.value.series[0].data = totalData
  chartData.value.series[1].data = levelOneData
  chartData.value.series[2].data = levelTwoData
  chartData.value.series[3].data = levelThreeData
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      confine: true,
      position: function (point, params, dom, rect, size) {
        // 确保tooltip不会被遮挡
        return [point[0] + 10, point[1] - 50]
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '总数',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.total
        },
        lineStyle: {
          color: colors.total,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '一级报警',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelOne
        },
        lineStyle: {
          color: colors.levelOne,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '二级报警',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelTwo
        },
        lineStyle: {
          color: colors.levelTwo,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '三级报警',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors.levelThree
        },
        lineStyle: {
          color: colors.levelThree,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 219, 35, 0.5)' },
              { offset: 1, color: 'rgba(255, 196, 35, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)

  window.addEventListener('resize', handleResize)
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 获取接口数据
const fetchAlarmTrendData = async () => {
  try {
    const response = await getMonitorAnalysisTrendStatistics()
    if (response?.data?.statistics) {
      const statistics = response.data.statistics

      // 更新图表数据
      chartData.value.xAxis = statistics.map(item => item.date.substring(5)) // 只显示月-日
      chartData.value.series[0].data = statistics.map(item => item.totalCount)
      chartData.value.series[1].data = statistics.map(item => item.level1Count)
      chartData.value.series[2].data = statistics.map(item => item.level2Count)
      chartData.value.series[3].data = statistics.map(item => item.level3Count)

      // 更新图表
      if (chartInstance) {
        updateChart()
      }
    }
  } catch (error) {
    console.error('获取报警趋势数据失败:', error)
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

onMounted(async () => {
  await nextTick()
  // 先生成数据
  await fetchAlarmTrendData()
  // 初始化图表
  initChart()
})
</script>

<style scoped>
.gas-monitoring-right-middle-panel {
  height: 310px;
  /* 默认高度为310px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 5px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.total {
  background: #055ADB;
}

.legend-icon.level-one {
  background: #FF311D;
}

.legend-icon.level-two {
  background: #FF6817;
}

.legend-icon.level-three {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-right-middle-panel {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-right-middle-panel {
    height: 290px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-right-middle-panel {
    height: 350px;
  }
}
</style>