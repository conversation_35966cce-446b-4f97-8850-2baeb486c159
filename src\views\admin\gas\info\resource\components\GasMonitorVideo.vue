<template>
  <div class="gas-monitor-video">
    <!-- 搜索区域 -->
    <div class="search-section">
      <GasMonitorVideoSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container" ref="tableContainerRef">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        :height="tableHeight"
        :scrollbar-always-on="true"
        :fit="false"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="设备名称" min-width="120" />
        <el-table-column prop="code" label="设备编号" min-width="120" />
        <el-table-column prop="target" label="监测对象" min-width="120" />
        <el-table-column prop="position" label="位置" min-width="150" />
        <el-table-column prop="status" label="设备状态" min-width="100">
          <template #default="{ row }">
            {{ getStatusText(row.status) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { ElTable, ElTableColumn, ElPagination } from 'element-plus';
import GasMonitorVideoSearch from './GasMonitorVideoSearch.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 表格容器引用
const tableContainerRef = ref(null);
const tableHeight = ref(400);

// 查询参数
const queryParams = ref({});

// 模拟数据
const mockData = [
  // {
  //   name: '视频监控设备1',
  //   code: 'VID001',
  //   target: '燃气管道',
  //   position: '经度: 120.123, 纬度: 30.456',
  //   status: '001'
  // }
];

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '001': '使用中',
    '002': '未使用',
    '003': '已废弃'
  };
  return statusMap[status] || '未知';
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  fetchVideoData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchVideoData();
};

// 获取视频监控数据
const fetchVideoData = async () => {
  // TODO: 替换为实际API调用
  tableData.value = mockData;
  total.value = mockData.length;
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchVideoData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchVideoData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleEdit = (row) => {
  console.log('编辑:', row);
};

const handleDetail = (row) => {
  console.log('详情:', row);
};

const handleDelete = (row) => {
  console.log('删除:', row);
};

const handleLocation = (row) => {
  console.log('定位:', row);
};

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainerRef.value) {
      // 获取容器的实际高度，表格应该填充整个容器
      const containerHeight = tableContainerRef.value.offsetHeight;
      // 直接使用容器高度，不需要预留空间，因为容器已经通过flex布局确定了正确高度
      tableHeight.value = Math.max(containerHeight, 200);
    }
  });
};

// 设置滚动同步
const setupScrollSync = () => {
  nextTick(() => {
    const tableContainer = tableContainerRef.value;
    if (tableContainer) {
      const elTable = tableContainer.querySelector('.el-table');
      const headerWrapper = elTable?.querySelector('.el-table__header-wrapper');
      const bodyWrapper = elTable?.querySelector('.el-table__body-wrapper');
      
      if (headerWrapper && bodyWrapper) {
        // 隐藏表头的水平滚动条
        headerWrapper.style.overflowX = 'hidden';
        
        // 监听表体滚动
        const syncScroll = () => {
          if (headerWrapper && bodyWrapper) {
            headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
          }
        };
        
        bodyWrapper.addEventListener('scroll', syncScroll);
        
        // 存储清理函数
        tableContainerRef.value._cleanup = () => {
          bodyWrapper.removeEventListener('scroll', syncScroll);
        };
      }
    }
  });
};

// 窗口大小变化处理
const handleResize = () => {
  calculateTableHeight();
};

onMounted(() => {
  fetchVideoData();
  calculateTableHeight();
  setupScrollSync();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (tableContainerRef.value?._cleanup) {
    tableContainerRef.value._cleanup();
  }
});
</script>

<style scoped>
.gas-monitor-video {
  width: 97%;
  height: 85%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  min-height: 600px;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

/* 表格样式 - 自适应高度 */
.table-container {
  flex: 1;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

:deep(.el-table) {
  flex: 1;
  overflow: hidden !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 自定义滚动条样式 */
:deep(.el-table__body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}


/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}
</style>