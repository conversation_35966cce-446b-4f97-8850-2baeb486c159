<template>
  <div class="gas-info-resource-danger">
    <!-- 搜索区域 -->
    <div class="search-section">
      <DangerSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 按钮区域 -->
    <div class="action-container">
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container" ref="tableContainer">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        :height="tableHeight"
        :fit="false"
        :scrollbar-always-on="true"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="dangerCode" label="危险源编码" min-width="120" />
        <el-table-column prop="dangerName" label="危险源名称" min-width="120" />
        <el-table-column label="建筑类型" min-width="120">
          <template #default="scope">
            {{ getBuildingTypeName(scope.row.buildingType) }}
          </template>
        </el-table-column>
        <el-table-column prop="featureDesc" label="特征描述" min-width="150" />
        <el-table-column prop="isMajor" label="是否重大危险源" min-width="140">
          <template #default="scope">
            {{ scope.row.isMajor === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="managementUnitName" label="所属单位" min-width="220" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.managementUnitName" class="text-ellipsis">
              {{ scope.row.managementUnitName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="contactUser" label="联系人" min-width="100" />
        <el-table-column prop="contactInfo" label="联系电话" min-width="120" />
        <el-table-column label="位置" min-width="250">
          <template #default="scope">
            <span v-if="scope.row.longitude && scope.row.latitude">
              经度: {{ scope.row.longitude }}, 纬度: {{ scope.row.latitude }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 - 固定在底部 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
    
    <!-- 危险源对话框 -->
    <DangerDialog 
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage, ElMessageBox, ElButton } from 'element-plus';
import DangerSearch from './components/DangerSearch.vue';
import DangerDialog from './components/DangerDialog.vue';
import { getGasDangerPage, deleteGasDanger, getGasDangerDetail } from '@/api/gas';
import { BUILDING_TYPE_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks';

// 表格加载状态
const loading = ref(false);

// 表格高度相关
const tableContainer = ref(null);
const tableHeight = ref(400);

// 表格横向滚动同步
const setupScrollSync = () => {
  nextTick(() => {
    if (tableContainer.value) {
      const headerWrapper = tableContainer.value.querySelector('.el-table__header-wrapper');
      const bodyWrapper = tableContainer.value.querySelector('.el-table__body-wrapper');
      
      if (headerWrapper && bodyWrapper) {
        // 监听表体横向滚动，同步表头滚动
        bodyWrapper.addEventListener('scroll', (e) => {
          headerWrapper.scrollLeft = e.target.scrollLeft;
        });
      }
    }
  });
};

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainer.value) {
      // 获取容器的实际高度，表格应该填充整个容器
      const containerHeight = tableContainer.value.offsetHeight;
      // 直接使用容器高度，不需要预留空间，因为容器已经通过flex布局确定了正确高度
      tableHeight.value = Math.max(containerHeight, 200);
    }
  });
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add', 'edit', 'view'
const currentData = ref({});

// 获取建筑类型名称
const getBuildingTypeName = (type) => {
  return BUILDING_TYPE_MAP[type] || '-';
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchDangerData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchDangerData();
};

// 获取危险源数据
const fetchDangerData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };
    
    const res = await getGasDangerPage(params);
    if (res && res.data) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取危险源数据失败:', error);
    ElMessage.error('获取危险源数据失败');
  } finally {
    loading.value = false;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchDangerData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchDangerData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
  if (row.longitude && row.longitude !== '' && row.latitude && row.latitude !== '') {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  }
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  currentData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    loading.value = true;
    const res = await getGasDangerDetail(row.id);
    if (res && res.data) {
      currentData.value = res.data;
      dialogMode.value = 'edit';
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取危险源详情失败:', error);
    ElMessage.error('获取危险源详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    loading.value = true;
    const res = await getGasDangerDetail(row.id);
    if (res && res.data) {
      currentData.value = res.data;
      dialogMode.value = 'view';
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取危险源详情失败:', error);
    ElMessage.error('获取危险源详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该危险源吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;
      await deleteGasDanger(row.id);
      ElMessage.success('删除成功');
      fetchDangerData();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    } finally {
      loading.value = false;
    }
  }).catch(() => {});
};

// 处理定位
const handleLocation = (row) => {
  if (row.longitude && row.longitude !== '' && row.latitude && row.latitude !== '') {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  } else {
    ElMessage.warning('没有经纬度，无法定位！');
  }
};

// 对话框成功提交回调
const handleDialogSuccess = () => {
  fetchDangerData();
};

onMounted(() => {
  fetchDangerData();
  calculateTableHeight();
  setupScrollSync();
  
  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight);
});

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight);
});
</script>

<style scoped>
.gas-info-resource-danger {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  background-color: white;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.action-container {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
  flex-shrink: 0;
}

/* 表格样式 - 自适应高度 */
.table-container {
  flex: 1;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  flex-shrink: 0;
  min-height: 32px;
}

:deep(.el-table) {
  height: 100% !important;
}

/* 隐藏表头横向滚动条，但保持内容可以滚动 */
:deep(.el-table__header-wrapper) {
  overflow-x: hidden !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none; /* Chrome, Safari, Opera */
}

/* 优化表体滚动条样式 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  overflow-x: auto !important;
  scrollbar-width: thin; /* Firefox */
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #909399;
}

/* 确保表头和表体水平滚动同步 */
:deep(.el-table__body-wrapper) {
  position: relative;
}

/* 当表体横向滚动时，通过 JavaScript 同步表头滚动 */

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-scrollbar__wrap) {
  overflow-x: auto !important;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  height: 6px !important;
  border-radius: 3px;
}

:deep(.el-scrollbar__bar.is-vertical) {
  width: 6px !important;
  border-radius: 3px;
}

:deep(.el-scrollbar__thumb) {
  background-color: #c0c4cc;
  border-radius: 3px;
}

:deep(.el-scrollbar__thumb:hover) {
  background-color: #909399;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>