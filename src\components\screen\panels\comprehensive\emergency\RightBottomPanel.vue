<template>
  <PanelBox title="应急预案库">
    <div class="panel-content">
      <!-- 顶部数据统计区域 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner blue"></span></span>
            <span class="stat-label">全部</span>
          </div>
          <span class="stat-value blue-gradient">{{ statisticsData.totalCount }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner cyan"></span></span>
            <span class="stat-label">国家级预案</span>
          </div>
          <span class="stat-value cyan-gradient">{{ statisticsData.countryCount }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner purple"></span></span>
            <span class="stat-label">省市预案</span>
          </div>
          <span class="stat-value purple-gradient">{{ statisticsData.provinceCount }}</span>
        </div>
        <div class="stat-item">
          <div class="stat-info">
            <span class="stat-dot"><span class="stat-dot-inner yellow"></span></span>
            <span class="stat-label">地方预案</span>
          </div>
          <span class="stat-value yellow-gradient">{{ statisticsData.cityCount }}</span>
        </div>
      </div>

      <!-- 专项分类区域 -->
      <div class="category-row">
        <div 
          v-for="(item, index) in categoryList" 
          :key="index" 
          class="category-item"
          :class="{ active: selectedCategory === item.type }"
          @click="handleCategoryClick(item.type)"
        >
          <div class="category-bg"></div>
          <div class="category-content">
            <div class="category-name">{{ item.name }}</div>
            <div class="category-value">{{ item.count }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <div class="search-input">
          <input type="text" v-model="searchText" placeholder="请输入预案名称" />
          <div class="search-icon">
            <i class="icon-search"></i>
          </div>
        </div>
      </div>

      <!-- 预案列表区域 -->
      <div class="plan-list">
        <ScrollTable
          :columns="tableColumns"
          :data="filteredPlanList"
          :autoScroll="true"
          :scrollSpeed="2000"
          :tableHeight="tableHeight"
          :visibleRows="visibleRows"
          :hiddenHeader="true"
          @row-click="openSchemeDetail"
        >
          <template #custom="{ row }">
            <div class="plan-row">
              <div class="plan-dot-container">
                <span class="plan-dot"><span class="plan-dot-inner" :style="{ background: getCategoryColor(row.type) }"></span></span>
              </div>
              <div class="plan-type">{{ row.type }}</div>
              <div class="plan-name" :title="row.name">{{ row.name }}</div>
              <div class="plan-arrow">
                <i class="icon-arrow-right"></i>
              </div>
            </div>
          </template>
        </ScrollTable>
      </div>
    </div>
    
    <!-- 预案详情弹窗 -->
    <SchemeDetailModal 
      v-if="showSchemeDetail" 
      :schemeData="selectedSchemeData" 
      @close="closeSchemeDetail" 
    />
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'
import SchemeDetailModal from './SchemeDetailModal.vue'
import { getEmergencySchemeStatistics, getEmergencySchemeList } from '@/api/comprehensive.js'

// 统计数据
const statisticsData = ref({
  totalCount: 0,
  countryCount: 0,
  provinceCount: 0,
  cityCount: 0
})

// 专项分类数据
const categoryList = ref([
  { name: '燃气', count: 0, type: '燃气' },
  { name: '排水', count: 0, type: '排水' },
  { name: '供热', count: 0, type: '供热' },
  { name: '桥梁', count: 0, type: '桥梁' }
])

// 搜索文本
const searchText = ref('')

// 当前选中的专项分类
const selectedCategory = ref('')

// 预案列表数据
const planList = ref([])

// 弹窗相关状态
const showSchemeDetail = ref(false)
const selectedSchemeData = ref({})

// 表格配置
const tableColumns = [
  { title: '预案信息', dataIndex: 'custom', width: '100%' }
]

// 根据专项分类获取对应颜色
const getCategoryColor = (type) => {
  const colorMap = {
    '燃气': '#00AAFF',
    '排水': '#00E0A0',
    '供热': '#FFD200',
    '桥梁': '#FF5F2E'
  }
  return colorMap[type] || '#00AAFF'
}

// 处理专项分类点击
const handleCategoryClick = (type) => {
  selectedCategory.value = selectedCategory.value === type ? '' : type
}

// 打开预案详情
const openSchemeDetail = (row) => {
  console.log('查看预案详情:', row)
  selectedSchemeData.value = row.originalData || row
  showSchemeDetail.value = true
}

// 关闭预案详情弹窗
const closeSchemeDetail = () => {
  showSchemeDetail.value = false
  selectedSchemeData.value = {}
}

// 过滤后的预案列表
const filteredPlanList = computed(() => {
  let result = planList.value

  // 根据选中的专项分类过滤
  if (selectedCategory.value) {
    result = result.filter(item => item.type === selectedCategory.value)
  }

  // 根据搜索文本过滤
  if (searchText.value) {
    const keyword = searchText.value.toLowerCase()
    result = result.filter(item => 
      item.name.toLowerCase().includes(keyword) || 
      item.type.toLowerCase().includes(keyword)
    )
  }

  return result
})

// 动态计算表格高度
const tableHeight = computed(() => {
  if (window.innerHeight >= 1056 && window.innerHeight <= 1080) {
    return '280px'
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return '220px'
  } else if (window.innerHeight >= 910 && window.innerHeight <= 939) {
    return '180px'
  } else {
    return '200px'
  }
})

// 动态计算可见行数
const visibleRows = computed(() => {
  if (window.innerHeight >= 1056 && window.innerHeight <= 1080) {
    return 3
  } else if (window.innerHeight >= 940 && window.innerHeight <= 1055) {
    return 2
  } else if (window.innerHeight >= 910 && window.innerHeight <= 939) {
    return 2
  } else {
    return 4
  }
})

// 获取预案库统计数据
const fetchStatisticsData = async () => {
  try {
    console.log('开始获取预案库统计数据')
    
    const response = await getEmergencySchemeStatistics()
    
    if (response.code === 200 && response.data) {
      statisticsData.value = {
        totalCount: response.data.totalCount || 0,
        countryCount: response.data.countryCount || 0,
        provinceCount: response.data.provinceCount || 0,
        cityCount: response.data.cityCount || 0
      }
      
      // 更新专项分类统计
      categoryList.value = [
        { name: '燃气', count: response.data.gasCount || 0, type: '燃气' },
        { name: '排水', count: response.data.drainCount || 0, type: '排水' },
        { name: '供热', count: response.data.heatCount || 0, type: '供热' },
        { name: '桥梁', count: response.data.bridgeCount || 0, type: '桥梁' }
      ]
      
      console.log('预案库统计数据获取成功:', statisticsData.value)
    } else {
      console.error('获取预案库统计数据失败:', response.message)
    }
  } catch (error) {
    console.error('获取预案库统计数据异常:', error)
  }
}

// 获取预案列表数据
const fetchPlanListData = async () => {
  try {
    console.log('开始获取预案列表数据')
    
    const response = await getEmergencySchemeList()
    
    if (response.code === 200 && response.data) {
      // 处理数据格式，适配表格显示
      planList.value = response.data.map((item, index) => ({
        id: item.id,
        type: item.relatedBusinessName || '-',
        name: item.schemeTitle || '-',
        // 保留原始数据用于详情显示
        originalData: item
      }))
      
      console.log('预案列表数据获取成功:', planList.value.length, '条')
    } else {
      console.error('获取预案列表数据失败:', response.message)
      // 使用默认数据作为备用
      planList.value = [
        { type: '燃气', name: '燃气位置专项预案', id: 1 },
        { type: '排水', name: '供水应急专项预案', id: 2 }
      ]
    }
  } catch (error) {
    console.error('获取预案列表数据异常:', error)
    // 使用默认数据作为备用
    planList.value = [
      { type: '燃气', name: '燃气位置专项预案', id: 1 },
      { type: '排水', name: '供水应急专项预案', id: 2 }
    ]
  }
}

// 初始化数据
const initData = async () => {
  await Promise.all([
    fetchStatisticsData(),
    fetchPlanListData()
  ])
}

onMounted(() => {
  // 初始化时获取数据
  initData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 统计数据样式 */
.stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 5px;
}

.stat-item {
  display: flex;
  align-items: center;
  position: relative;
  gap: 8px;
}

.stat-dot {
  position: relative;
  width: 9px;
  height: 9px;
  margin-bottom: 2px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}

.stat-dot-inner.blue { 
  background: #00AAFF;
}

.stat-dot-inner.cyan { 
  background: #00E0A0;
}

.stat-dot-inner.purple { 
  background: #8560FF;
}

.stat-dot-inner.yellow { 
  background: #FFD200;
}

.stat-dot:has(.stat-dot-inner.blue) {
  background: rgba(0, 170, 255, 0.4);
}

.stat-dot:has(.stat-dot-inner.cyan) {
  background: rgba(0, 224, 160, 0.4);
}

.stat-dot:has(.stat-dot-inner.purple) {
  background: rgba(133, 96, 255, 0.4);
}

.stat-dot:has(.stat-dot-inner.yellow) {
  background: rgba(255, 210, 0, 0.4);
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  font-style: normal;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
}

.blue-gradient {
  background: linear-gradient(90deg, #00AAFF 0%, #A6E8FF 100%);
  -webkit-background-clip: text;
}

.cyan-gradient {
  background: linear-gradient(90deg, #00E0A0 0%, #A6FFE0 100%);
  -webkit-background-clip: text;
}

.purple-gradient {
  background: linear-gradient(90deg, #8560FF 0%, #CDB9FF 100%);
  -webkit-background-clip: text;
}

.yellow-gradient {
  background: linear-gradient(90deg, #FFD200 0%, #FFF0A6 100%);
  -webkit-background-clip: text;
}

/* 专项分类区域样式 */
.category-row {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.category-item {
  position: relative;
  width: 22%;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.category-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('@/assets/images/screen/comprehensive/yuan_bg.png') no-repeat center center;
  background-size: 100% 100%;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.category-item.active .category-bg {
  opacity: 1;
}

.category-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 80%;
}

.category-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  line-height: 17px;
}

.category-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 20px;
  color: #FFFFFF;
}

/* 搜索区域样式 */
.search-area {
  margin-top: 5px;
}

.search-input {
  position: relative;
  width: 100%;
  height: 32px;
}

.search-input input {
  width: 100%;
  height: 100%;
  background: rgba(0, 57, 116, 0.3);
  border: 1px solid rgba(0, 170, 255, 0.5);
  border-radius: 4px;
  padding: 0 35px 0 10px;
  font-family: PingFangSC, PingFang SC;
  font-size: 14px;
  color: #FFFFFF;
  outline: none;
}

.search-input input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-search {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzAwQUFGRiIgZD0iTTE1LjUgMTRoLS43OWwtLjI4LS4yN0E2LjQ3MSA2LjQ3MSAwIDAgMCAxNiA5LjUgNi41IDYuNSAwIDEgMCA5LjUgMTZjMS42MSAwIDMuMDktLjU5IDQuMjMtMS41N2wuMjcuMjh2Ljc5bDUgNC45OUwyMC40OSAxOWwtNC45OS01em0tNiAwQzcuMDEgMTQgNSAxMS45OSA1IDkuNVM3LjAxIDUgOS41IDUgMTQgNy4wMSAxNCA5LjUgMTEuOTkgMTQgOS41IDE0eiIvPjwvc3ZnPg==') no-repeat center;
  background-size: contain;
}

/* 预案列表区域样式 */
.plan-list {
  flex: 1;
  margin-top: 5px;
}

.plan-row {
  display: flex;
  align-items: center;
  height: 2rem;
  padding: 0 10px;
  /* background: rgba(0, 57, 116, 0.4); */
  margin-bottom: 5px;
  border-radius: 2px;
}

.plan-dot-container {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.plan-dot {
  position: relative;
  width: 10px;
  height: 10px;
  border: 1px solid #00AAFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-dot-inner {
  width: 4px;
  height: 4px;
  background: #00AAFF;
}

.plan-type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 10px;
  white-space: nowrap;
  width: 50px;
}

.plan-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plan-arrow {
  margin-left: 10px;
}

.icon-arrow-right {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzAwQUFGRiIgZD0iTTguNTkgMTYuNTlMMTMuMTcgMTIgOC41OSA3LjQxIDEwIDZsNiA2LTYgNi0xLjQxLTEuNDF6Ii8+PC9zdmc+') no-repeat center;
  background-size: contain;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .stat-value {
    font-size: 24px;
    line-height: 26px;
  }
  
  .category-item {
    height: 35px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
    gap: 12px;
  }
  
  .stat-value {
    font-size: 22px;
    line-height: 24px;
  }
  
  .category-item {
    height: 30px;
  }
  
  .category-value {
    font-size: 18px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
    gap: 18px;
  }
  
  .stat-value {
    font-size: 28px;
    line-height: 30px;
  }
  
  .category-item {
    height: 40px;
  }
  
  .category-value {
    font-size: 22px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
    gap: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }

  .stat-value {
    font-size: 20px;
    line-height: 22px;
  }
  
  .category-value {
    font-size: 18px;
  }
}

@media screen and (min-height: 910px) and (max-height: 939px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }

  .stat-value {
    font-size: 18px;
    line-height: 20px;
  }
  
  .category-item {
    height: 28px;
  }
  
  .category-value {
    font-size: 16px;
  }
  
  .category-name {
    font-size: 12px;
  }
}
</style>