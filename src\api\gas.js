import request from '@/utils/request'
import moment from 'moment'

// 获取管点信息分页数据
export function getGasNetworkPointPage(params) {
  const { pageNum, pageSize, pointType, managementUnit, pointCode } = params;
  return request({
    url: `/gas/usmZyGasPoint/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      pointType,
      managementUnit,
      pointCode
    }
  });
}

// 新增管点信息
export function saveGasNetworkPoint(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasPoint/save',
    method: 'post',
    data: formattedData
  });
}

// 更新管点信息
export function updateGasNetworkPoint(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasPoint/update',
    method: 'post',
    data: formattedData
  });
}

// 获取管点详情
export function getGasNetworkPointDetail(id) {
  return request({
    url: `/gas/usmZyGasPoint/${id}`,
    method: 'get'
  });
}

// 删除管点
export function deleteGasNetworkPoint(id) {
  return request({
    url: `/gas/usmZyGasPoint/${id}`,
    method: 'delete'
  });
}

export function getInfrastructureStatistics() {
  return request({
    url: '/gas/api/v1/situation/infrastructure/statistics',
    method: 'get'
  })
}

export function getPipelineStatistics() {
  return request({
    url: '/gas/api/v1/situation/pipeline/statistics',
    method: 'get'
  })
}

export function getStationStatistics() {
  return request({
    url: '/gas/api/v1/situation/station/statistics',
    method: 'get'
  })
}

// 获取管网风险统计
export function getPipelineRiskStatistics() {
  return request({
    url: '/gas/api/v1/situation/pipeline/risk/statistics',
    method: 'get'
  })
}

// 获取场站风险统计
export function getStationRiskStatistics() {
  return request({
    url: '/gas/api/v1/situation/station/risk/statistics',
    method: 'get'
  })
}

// 获取风险监测统计
export function getRiskMonitorStatistics() {
  return request({
    url: '/gas/api/v1/situation/monitor/device/statistics',
    method: 'get'
  });
}

// // 获取专家信息分页列表
// export function getGasExpertPage(params) {
//   return request({
//     url: '/gas/usmFzGasExpert/page',
//     method: 'get',
//     params
//   });
// }

// 获取专家信息详情
export function getGasExpertDetail(id) {
  return request({
    url: `/gas/usmFzGasExpert/${id}`,
    method: 'get'
  });
}

// 新增专家信息
export function saveGasExpert(data) {
  return request({
    url: '/gas/usmFzGasExpert/save',
    method: 'post',
    data
  });
}

// 更新专家信息
export function updateGasExpert(data) {
  return request({
    url: '/gas/usmFzGasExpert/update',
    method: 'post',
    data
  });
}

// 删除专家信息
export function deleteGasExpert(id) {
  return request({
    url: `/gas/usmFzGasExpert/${id}`,
    method: 'delete'
  });
}

// 获取监测报警统计
export function getAlarmStatistics() {
  return request({
    url: '/gas/api/v1/situation/monitor/alarm/statistics',
    method: 'get'
  })
}

// 获取报警处置状态统计
export function getAlarmStatusStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/statistics',
    method: 'post',
    data: params
  })
}

// 获取报警等级统计
export function getAlarmLevelStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/level/statistics',
    method: 'post',
    data: params
  })
}

// 获取报警列表数据
export function getAlarmList(page, size, params) {
  return request({
    url: `/gas/usmMonitorAlarm/search/${page}/${size}`,
    method: 'post',
    data: params
  })
}
// 获取报警类型
export function getAlarmType() {
  return request({
    url: `/gas/usmMonitorAlarm/getAlarmType`,
    method: 'get'
  })
}
// 获取监测分析统计数据
export function getMonitorAnalysisStatistics(params = { dayIndex: 30, pageNum: 1, pageSize: 10 }) {
  return request({
    url: '/gas/api/v1/monitor/analysis/statistics',
    method: 'post',
    params: params
  })
}

// 获取监测设备分析统计
export function getMonitorAnalysisDeviceStatistics() {
  return request({
    url: '/gas/api/v1/monitor/analysis/device/statistics',
    method: 'get'
  })
}

// 获取场站分页数据
export function getGasStationPage(params) {
  const { pageNum, pageSize, stationType, managementUnit, name } = params;
  return request({
    url: `/gas/usmZyGasStation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      stationType,
      managementUnit,
      name
    }
  });
}

// 新增场站信息
export function saveGasStation(data) {
  const formattedData = { ...data };
  if (formattedData.operationTime) {
    formattedData.operationTime = moment(formattedData.operationTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasStation/save',
    method: 'post',
    data: formattedData
  });
}

// 更新场站信息
export function updateGasStation(data) {
  const formattedData = { ...data };
  if (formattedData.operationTime) {
    formattedData.operationTime = moment(formattedData.operationTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasStation/update',
    method: 'post',
    data: formattedData
  });
}

// 获取场站详情
export function getGasStationDetail(id) {
  return request({
    url: `/gas/usmZyGasStation/${id}`,
    method: 'get'
  });
}

// 删除场站
export function deleteGasStation(id) {
  return request({
    url: `/gas/usmZyGasStation/${id}`,
    method: 'delete'
  });
}

// 获取燃气管线分页数据
export function getGasPipelinePage(params) {
  const { pageNum, pageSize, buriedType, pressureLevel, material, managementUnit, pipeDiameter, pipelineCode } = params;
  return request({
    url: `/gas/usmZyGasPipeline/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      buriedType,
      pressureLevel,
      material,
      managementUnit,
      pipeDiameter,
      pipelineCode
    }
  });
}
export function getGasPipelineList(data) {
  return request({
    url: `/gas/usmZyGasPipeline/list`,
    method: 'post',
    data: data
  });
}

// 获取燃气窖井列表数据
export function getGasWellList(data) {
  return request({
    url: `/gas/usmZyGasWell/list`,
    method: 'post',
    data: data
  });
}

// 获取传感器设备分页数据
export function getSensorDevicePage(params) {
  const { pageNum, pageSize, deviceType, onlineStatus, deviceName, indexCode } = params;
  return request({
    url: `/gas/usmMonitorDevice/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      deviceType,
      onlineStatus,
      deviceName,
      indexCode
    }
  });
}

// 获取传感器设备详情
export function getSensorDeviceDetail(id) {
  return request({
    url: `/gas/usmMonitorDevice/${id}`,
    method: 'get'
  });
}

// 新增传感器设备
export function saveSensorDevice(data) {
  return request({
    url: `/gas/usmMonitorDevice/save`,
    method: 'post',
    data: data
  });
}

// 更新传感器设备
export function updateSensorDevice(data) {
  return request({
    url: `/gas/usmMonitorDevice/update`,
    method: 'post',
    data: data
  });
}

// 删除传感器设备
export function deleteSensorDevice(id) {
  return request({
    url: `/gas/usmMonitorDevice/${id}`,
    method: 'delete'
  });
}

// 新增燃气管线
export function saveGasPipeline(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasPipeline/save',
    method: 'post',
    data: formattedData
  });
}

// 更新燃气管线
export function updateGasPipeline(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasPipeline/update',
    method: 'post',
    data: formattedData
  });
}

// 获取权属单位列表
export function getManagementUnits() {
  return request({
    url: '/gas/usmEnterpriseBasicInfo/list',
    method: 'post',
    data: {}
  });
}

// 获取燃气管线详情
export function getGasPipelineDetail(id) {
  return request({
    url: `/gas/usmZyGasPipeline/${id}`,
    method: 'get'
  });
}

// 删除燃气管线
export function deleteGasPipeline(id) {
  return request({
    url: `/gas/usmZyGasPipeline/${id}`,
    method: 'delete'
  });
}

// 获取窨井信息分页数据
export function getGasWellPage(params) {
  const { pageNum, pageSize, wellMaterial, wellShape, managementUnit, wellCode } = params;
  return request({
    url: `/gas/usmZyGasWell/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      wellMaterial,
      wellShape,
      managementUnit,
      wellCode
    }
  });
}

// 新增窨井信息
export function saveGasWell(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasWell/save',
    method: 'post',
    data: formattedData
  });
}

// 更新窨井信息
export function updateGasWell(data) {
  const formattedData = { ...data };
  if (formattedData.installTime) {
    formattedData.installTime = moment(formattedData.installTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasWell/update',
    method: 'post',
    data: formattedData
  });
}

// 获取窨井详情
export function getGasWellDetail(id) {
  return request({
    url: `/gas/usmZyGasWell/${id}`,
    method: 'get'
  });
}

// 删除窨井
export function deleteGasWell(id) {
  return request({
    url: `/gas/usmZyGasWell/${id}`,
    method: 'delete'
  });
}

// 监测设备在线状态
export const DEVICE_ONLINE_STATUS = [
  { label: '在线', value: 0 },
  { label: '离线', value: 1 }
];

export const DEVICE_ONLINE_STATUS_MAP = {
  0: '在线',
  1: '离线'
};

// 获取报警等级统计数据
export function getMonitorAnalysisLevelStatistics(dayIndex = 30) {
  return request({
    url: '/gas/api/v1/monitor/analysis/level/statistics',
    method: 'post',
    data: { dayIndex }
  });
}

// 获取报警趋势统计数据
export function getMonitorAnalysisTrendStatistics(dayIndex = 7) {
  return request({
    url: '/gas/api/v1/monitor/analysis/trend/statistics',
    method: 'post',
    data: { dayIndex }
  });
}

/**
 * 燃气风险评估相关接口
 */

// 获取风险评估管线列表数据
export function getRiskAssessmentPipelineList(data) {
  return request({
    url: '/gas/usmRiskAssessmentPipeline/list',
    method: 'post',
    data
  });
}

// 获取燃气管网维修记录分页数据
export function getGasRepairPage(params) {
  const { pageNum, pageSize, repairType, managementUnit, repairCode } = params;
  return request({
    url: `/gas/usmZyGasRepair/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      repairType,
      managementUnit,
      repairCode
    }
  });
}

// 新增维修记录
export function saveGasRepair(data) {
  const formattedData = { ...data };
  if (formattedData.repairTime) {
    formattedData.repairTime = moment(formattedData.repairTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasRepair/save',
    method: 'post',
    data: formattedData
  });
}

// 更新维修记录
export function updateGasRepair(data) {
  const formattedData = { ...data };
  if (formattedData.repairTime) {
    formattedData.repairTime = moment(formattedData.repairTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasRepair/update',
    method: 'post',
    data: formattedData
  });
}

// 获取维修记录详情
export function getGasRepairDetail(id) {
  return request({
    url: `/gas/usmZyGasRepair/${id}`,
    method: 'get'
  });
}

// 删除维修记录
export function deleteGasRepair(id) {
  return request({
    url: `/gas/usmZyGasRepair/${id}`,
    method: 'delete'
  });
}

/**
 * 管网统计相关接口
 */

// 获取管线长度统计
export function getPipelineLengthStatistics() {
  return request({
    url: '/gas/usmZyGasPipeline/pipelineLengthStatistics',
    method: 'get'
  });
}

// 获取管线趋势统计
export function getPipelineLengthTrend() {
  return request({
    url: '/gas/usmZyGasPipeline/pipelineLengthTrend',
    method: 'get'
  });
}

// 获取管线压力级别统计
export function getPipelinePressureLevelStatistics() {
  return request({
    url: '/gas/usmZyGasPipeline/pipelinePressureLevelStatistics',
    method: 'get'
  });
}

// 获取管线材质统计
export function getPipelineMaterialStatistics() {
  return request({
    url: '/gas/usmZyGasPipeline/pipelineMaterialStatistics',
    method: 'get'
  });
}

// 获取管线年龄统计
export function getPipelineAgeStatistics() {
  return request({
    url: '/gas/usmZyGasPipeline/pipelineAgeStatistics',
    method: 'get'
  });
}

/**
 * 处置方案相关接口
 */

// 获取处置方案分页数据
export function getGasDisposalPlanPage(params) {
  const { pageNum, pageSize, alarmType, sourceUnit, schemeName } = params;
  return request({
    url: `/gas/usmFzGasScheme/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      alarmType,
      sourceUnit,
      schemeName
    }
  });
}

// 获取处置方案详情
export function getGasDisposalPlanDetail(id) {
  return request({
    url: `/gas/usmFzGasScheme/${id}`,
    method: 'get'
  });
}

// 新增处置方案
export function saveGasDisposalPlan(data) {
  const formattedData = { ...data };
  if (formattedData.editDate) {
    formattedData.editDate = moment(formattedData.editDate).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmFzGasScheme/save',
    method: 'post',
    data: formattedData
  });
}

// 更新处置方案
export function updateGasDisposalPlan(data) {
  const formattedData = { ...data };
  if (formattedData.editDate) {
    formattedData.editDate = moment(formattedData.editDate).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmFzGasScheme/update',
    method: 'post',
    data: formattedData
  });
}

// 删除处置方案
export function deleteGasDisposalPlan(id) {
  return request({
    url: `/gas/usmFzGasScheme/${id}`,
    method: 'delete'
  });
}

// 获取监测分析统计数据（带条件）
export function getMonitorAnalysisStatisticsCondition(params) {
  const { pageNum = 1, pageSize = 10, alarmCode, alarmLevel, alarmSource, startDate, endDate, town } = params;
  return request({
    url: '/gas/api/v1/monitor/analysis/statistics/condition',
    method: 'post',
    data: {
      alarmCode,
      alarmLevel,
      alarmSource,
      startDate,
      endDate,
      town,
      pageNum,
      pageSize
    }
  });
}

// 获取高发报警设备数据
export function getMonitorAnalysisDeviceHighFrequency(params = { dayIndex: 7, pageNum: 1, pageSize: 10 }) {
  return request({
    url: '/gas/api/v1/monitor/analysis/device/high-frequency',
    method: 'post',
    data: params
  });
}

// 获取专家信息分页数据
export function getGasExpertPage(params) {
  const { pageNum, pageSize, expertName, alarmType, belongUnit } = params;
  return request({
    url: `/gas/usmFzGasExpert/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      expertName,
      alarmType,
      belongUnit
    }
  });
}

// 获取报警详情
export function getAlarmDetail(id) {
  return request({
    url: `/gas/usmMonitorAlarm/${id}`,
    method: 'get'
  });
}

// 获取设备监测曲线
export function getMonitorCurve(params) {
  return request({
    url: '/gas/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  });
}

// 获取设备监测指标
export function getMonitorIndicators(deviceId) {
  return request({
    url: `/gas/usmMonitorRecord/monitorIndicators/${deviceId}`,
    method: 'get'
  });
}

// 获取设备报警记录
export function getAlarmRecord(deviceId) {
  return request({
    url: `/gas/usmMonitorAlarm/alarmRecord/${deviceId}`,
    method: 'get'
  });
}

// 获取设备统计数据
export function getDeviceStatistics() {
  return request({
    url: '/gas/usmMonitorDevice/deviceStatistics',
    method: 'get'
  });
}

// 获取设备类型统计数据
export function getDeviceStatisticsByType() {
  return request({
    url: '/gas/usmMonitorDevice/deviceStatisticsByType',
    method: 'get'
  });
}

// 获取设备实时监测分页数据
export function getDeviceRealTimeMonitoring(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmMonitorDevice/deviceRealTimeMonitoring/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

/**
 * 阈值管理相关接口
 */

// 获取报警阈值分页数据
export function getAlarmThresholdPage(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmAlarmThreshold/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 新增报警阈值
export function saveAlarmThreshold(data) {
  return request({
    url: '/gas/usmAlarmThreshold/save',
    method: 'post',
    data
  });
}

// 更新报警阈值
export function updateAlarmThreshold(data) {
  return request({
    url: '/gas/usmAlarmThreshold/update',
    method: 'post',
    data
  });
}

// 获取报警阈值详情
export function getAlarmThresholdDetail(id) {
  return request({
    url: `/gas/usmAlarmThreshold/${id}`,
    method: 'get'
  });
}

// 删除报警阈值
export function deleteAlarmThreshold(id) {
  return request({
    url: `/gas/usmAlarmThreshold/${id}`,
    method: 'delete'
  });
}

// 获取监测设备列表
export function getMonitorDeviceList(data) {
  return request({
    url: '/gas/usmMonitorDevice/list',
    method: 'post',
    data
  });
}

// 获取报警处理统计数据
export function getAlarmDisposalStatistics(params = {}) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/statistics',
    method: 'post',
    data: params
  })
}


// 是否重大危险源
export const IS_MAJOR_OPTIONS = [
  { label: '是', value: '1' },
  { label: '否', value: '0' }
];

// 获取危险源分页数据
export function getGasDangerPage(params) {
  const { pageNum, pageSize, buildingType, managementUnit, nameOrCode } = params;
  return request({
    url: `/gas/usmRiskDanger/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      buildingType,
      managementUnit,
      nameOrCode
    }
  });
}

// 获取危险源详情
export function getGasDangerDetail(id) {
  return request({
    url: `/gas/usmRiskDanger/${id}`,
    method: 'get'
  });
}

// 新增危险源
export function saveGasDanger(data) {
  return request({
    url: '/gas/usmRiskDanger/save',
    method: 'post',
    data
  });
}

// 更新危险源
export function updateGasDanger(data) {
  return request({
    url: '/gas/usmRiskDanger/update',
    method: 'post',
    data
  });
}

// 删除危险源
export function deleteGasDanger(id) {
  return request({
    url: `/gas/usmRiskDanger/${id}`,
    method: 'delete'
  });
}


// 获取防护目标分页数据
export function getProtectionPage(params) {
  const { pageNum, pageSize, buildingType, managementUnit, nameOrCode } = params;
  return request({
    url: `/gas/usmRiskProtect/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: {
      buildingType,
      managementUnit,
      nameOrCode
    }
  });
}

// 获取防护目标详情
export function getProtectionDetail(id) {
  return request({
    url: `/gas/usmRiskProtect/${id}`,
    method: 'get'
  });
}

// 新增防护目标
export function saveProtection(data) {
  return request({
    url: '/gas/usmRiskProtect/save',
    method: 'post',
    data
  });
}

// 更新防护目标
export function updateProtection(data) {
  return request({
    url: '/gas/usmRiskProtect/update',
    method: 'post',
    data
  });
}

// 删除防护目标
export function deleteProtection(id) {
  return request({
    url: `/gas/usmRiskProtect/${id}`,
    method: 'delete'
  });
}

/**
 * 场站风险评估相关接口
 */

// 获取场站风险评估分页数据
export function getStationRiskAssessmentPage(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmRiskAssessmentStation/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 获取场站风险评估详情
export function getStationRiskAssessmentDetail(id) {
  return request({
    url: `/gas/usmRiskAssessmentStation/${id}`,
    method: 'get'
  });
}

// 新增场站风险评估
export function saveStationRiskAssessment(data) {
  return request({
    url: '/gas/usmRiskAssessmentStation/save',
    method: 'post',
    data
  });
}

// 更新场站风险评估
export function updateStationRiskAssessment(data) {
  return request({
    url: '/gas/usmRiskAssessmentStation/update',
    method: 'post',
    data
  });
}

// 删除场站风险评估
export function deleteStationRiskAssessment(id) {
  return request({
    url: `/gas/usmRiskAssessmentStation/${id}`,
    method: 'delete'
  });
}

/**
 * 管网风险相关接口
 */

// 获取管网风险管线统计
export function getPipelineRiskPipelineStatistics() {
  return request({
    url: '/gas/api/v1/pipeline/risk/pipeline/statistics',
    method: 'get'
  });
}

// 获取管网风险按压力级别统计
export function getPipelineRiskPressureStatistics() {
  return request({
    url: '/gas/api/v1/pipeline/risk/pressure/statistics',
    method: 'get'
  });
}

// 获取管网风险按企业统计
export function getPipelineRiskEnterpriseStatistics() {
  return request({
    url: '/gas/api/v1/pipeline/risk/enterprise/statistics',
    method: 'get'
  });
}

// 获取报警统计分析 - 顶部卡片数据
export function getAlarmAnalysisStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/statistics',
    method: 'post',
    data: params
  });
}

// 获取报警统计分析 - 处置情况
export function getAlarmAnalysisDisposalSituation(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/statistics/disposalSituation',
    method: 'post',
    data: params
  });
}

// 获取报警趋势统计
export function getAlarmAnalysisTrendStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/trend/statistics',
    method: 'post',
    data: params
  });
}

// 获取报警级别统计
export function getAlarmAnalysisLevelStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/level/statistics',
    method: 'post',
    data: params
  });
}

// 获取高发报警设备统计
export function getAlarmAnalysisDeviceHighFrequency(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/device/high-frequency',
    method: 'post',
    data: params
  });
}

// 获取企业报警统计
export function getAlarmAnalysisEnterpriseStatistics(params) {
  return request({
    url: '/gas/usmFzAlarmAnalysis/enterprise/statistics',
    method: 'post',
    data: params
  });
}

// 获取设备监测曲线数据
export function getMonitorCurveData(params) {
  return request({
    url: '/gas/usmMonitorRecord/monitorCurve',
    method: 'post',
    data: params
  });
}

// 获取设备监测记录分页数据
export function getMonitorCurvePageData(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmMonitorRecord/monitorCurvePage/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 获取设备离线记录分页数据
export function getOfflineRecordsData(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmMonitorRecord/offlineRecords/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 获取区域管网风险统计
export function getPipelineRiskRegionStatistics() {
  return request({
    url: '/gas/api/v1/pipeline/risk/region/statistics',
    method: 'get'
  })
}

// 获取场站风险统计
export function getPipelineRiskStationStatistics() {
  return request({
    url: '/gas/api/v1/pipeline/risk/station/statistics',
    method: 'get'
  })
}

// 获取管网风险清单
export function getPipelineRiskPipelineList(params) {
  return request({
    url: '/gas/api/v1/pipeline/risk/pipeline/list',
    method: 'get',
    params
  })
}

// 获取场站风险清单
export function getPipelineRiskStationList(params) {
  return request({
    url: '/gas/api/v1/pipeline/risk/station/list',
    method: 'get',
    params
  })
}

/**
 * 安全报告相关接口
 */

// 获取安全报告分页数据
export function getSecurityReportPage(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmSecurityReport/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 下载安全报告文件
export function downloadSecurityReport(fileUrl) {
  return request({
    url: `/common/download?fileName=${encodeURIComponent(fileUrl)}`,
    method: 'get',
    responseType: 'blob'
  });
}

/**
 * 周边窨井相关接口
 */

// 获取周边窨井分页数据
export function getSurroundingWellPage(pageNum, pageSize, params) {
  return request({
    url: `/gas/usmZyGasSurroundingWell/page/${pageNum}/${pageSize}`,
    method: 'post',
    data: params
  });
}

// 新增周边窨井
export function saveSurroundingWell(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasSurroundingWell/save',
    method: 'post',
    data: formattedData
  });
}

// 更新周边窨井
export function updateSurroundingWell(data) {
  const formattedData = { ...data };
  if (formattedData.constructionTime) {
    formattedData.constructionTime = moment(formattedData.constructionTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return request({
    url: '/gas/usmZyGasSurroundingWell/update',
    method: 'post',
    data: formattedData
  });
}

// 获取周边窨井详情
export function getSurroundingWellDetail(id) {
  return request({
    url: `/gas/usmZyGasSurroundingWell/${id}`,
    method: 'get'
  });
}

// 删除周边窨井
export function deleteSurroundingWell(id) {
  return request({
    url: `/gas/usmZyGasSurroundingWell/${id}`,
    method: 'delete'
  });
}

// 导出周边窨井数据
export function exportSurroundingWell(params) {
  return request({
    url: '/gas/usmZyGasSurroundingWell/export',
    method: 'post',
    responseType: 'blob',
    data: params
  });
}

// 导入周边窨井数据
export function importSurroundingWell(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/gas/usmZyGasSurroundingWell/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 首页相关接口
 */

// 获取首页管网统计数据
export function getHomePagePipelineStatistics() {
  return request({
    url: '/gas/homePage/pipeline/statistics',
    method: 'get'
  });
}

// 获取首页报警数量统计
export function getHomePageAlarmCount() {
  return request({
    url: '/gas/homePage/alarm/count',
    method: 'get'
  });
}

// 获取首页未处理报警统计
export function getHomePageAlarmUnhandleStatistics(pageNum = 1, pageSize = 10) {
  return request({
    url: '/gas/homePage/alarm/unhandleStatistics',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  });
}

// 获取首页报警排名
export function getHomePageAlarmRank(pageNum = 1, pageSize = 10) {
  return request({
    url: '/gas/homePage/alarm/rank',
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  });
}

// 获取首页报警统计数据
export function getHomePageAlarmStatistics(dayIndex = 7) {
  return request({
    url: '/gas/homePage/alarm/statistics',
    method: 'get',
    params: {
      dayIndex
    }
  });
}

// 获取报警状态记录列表
export function getAlarmStatusList(data) {
  return request({
    url: '/gas/usmMonitorAlarmStatus/list',
    method: 'post',
    data
  });
}

// 获取监测指标列表
export function getMonitorIndicatorsList(data) {
  return request({
    url: '/gas/usmMonitorIndicators/list',
    method: 'post',
    data
  });
}

// 报警确认接口
export function confirmAlarm(data) {
  return request({
    url: '/gas/usmMonitorAlarm/alarm/confirm',
    method: 'post',
    data
  });
}

// 处置新增/编辑接口
export function saveOrUpdateAlarmHandle(data) {
  return request({
    url: '/gas/usmMonitorAlarm/alarm/handle',
    method: 'post',
    data
  });
}

// 处置删除接口
export function deleteAlarmHandle(id) {
  return request({
    url: `/gas/usmMonitorAlarm/alarm/handle/${id}`,
    method: 'delete'
  });
}

// 报警处置列表接口
export function getAlarmHandleList(id) {
  return request({
    url: `/gas/usmMonitorAlarm/alarm/handleList/${id}`,
    method: 'get'
  });
}