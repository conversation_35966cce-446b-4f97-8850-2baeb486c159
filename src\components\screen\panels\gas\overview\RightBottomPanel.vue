<template>
  <PanelBox title="监测报警" class="right-bottom-panel">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot blue"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">报警总数</span>
          <span class="stat-value-red">{{ stats.totalAlarms }}</span>
          <span class="stat-unit">个</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot cyan"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">已处理</span>
          <span class="stat-value-blue">{{ stats.handledAlarms }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot green"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">处理完成率</span>
          <span class="stat-value-green">{{ stats.handlingRate }}%</span>
        </div>
      </div>
      
      <div class="chart-container">
        <div class="chart-title-container">
          <!-- <span class="unit-label">单位（个）</span> -->
          <span class="chart-title">近30日报警趋势</span>
        </div>
        <div class="chart-wrapper" ref="chartRef"></div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import { getAlarmStatistics } from '@/api/gas.js'

// 监测报警面板组件
const chartRef = ref(null)
let chartInstance = null

// 动态数据
const stats = ref({
  totalAlarms: 0,
  handledAlarms: 0,
  handlingRate: 0,
  alarmTrends: []
})

const alertTrendData = {
  dates: [],
  values: []
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '18%',
      left: '10%',
      right: '4%',
      bottom: '1%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 16, 33, 0.8)',
      borderColor: 'rgba(0, 135, 255, 0.3)',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      confine: true,
      enterable: true,
      formatter: function(params) {
        const param = params[0];
        return `<div style="font-weight:bold;margin-bottom:8px;color:#3CF3FF;">${param.name}</div>
          <div style="display:flex;justify-content:space-between;margin:5px 0;align-items:center;">
            <span style="margin-right:15px;display:inline-block;">
              <span style="display:inline-block;width:8px;height:8px;border-radius:4px;background-color:#23CAFF;margin-right:5px;"></span>
              报警数量:
            </span>
            <span style="font-weight:bold;color:#23CAFF;">${param.value}个</span>
          </div>`;
      },
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(5, 122, 255, 0.1)'
        }
      }
    },
    xAxis: {
      type: 'category',
      data: alertTrendData.dates,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      name: '单位（个）',
      nameTextStyle: {
        color: "rgba(255, 255, 255, 0.6)",
        fontSize: 12,
        padding: [0, 30, -5, 0]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [{
      data: alertTrendData.values,
      type: 'line',
      smooth: true,
      symbol: 'none',
      itemStyle: {
        color: '#23CAFF'
      },
      lineStyle: {
        color: '#23CAFF',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(35,202,255,0.5)' },
            { offset: 1, color: 'rgba(35,202,255,0)' }
          ]
        }
      }
    }]
  }

  chartInstance.setOption(option)

  window.addEventListener('resize', handleResize)
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
    
    // 根据窗口高度动态调整图表bottom值
    const windowHeight = window.innerHeight
    let bottomSpace = '10%' // 默认值
    
    if (windowHeight <= 939 && windowHeight >= 900) {
      bottomSpace = '12%' // 在910px左右的屏幕上使用更大的底部间距
    } else if (windowHeight < 900) {
      bottomSpace = '15%' // 在更小的屏幕上使用更大的底部间距
    }
    
    chartInstance.setOption({
      grid: {
        bottom: bottomSpace
      }
    })
  }
}

// 从后端获取数据
const fetchAlertData = async () => {
  try {
    const res = await getAlarmStatistics()
    if (res.code === 200 && res.data) {
      stats.value.totalAlarms = res.data.totalAlarms
      stats.value.handledAlarms = res.data.handledAlarms
      stats.value.handlingRate = res.data.handlingRate
      stats.value.alarmTrends = res.data.alarmTrends || []
      alertTrendData.dates = stats.value.alarmTrends.map(item => item.date)
      alertTrendData.values = stats.value.alarmTrends.map(item => item.alarmCount)
      updateChart()
    } else {
      stats.value.totalAlarms = 0
      stats.value.handledAlarms = 0
      stats.value.handlingRate = 0
      stats.value.alarmTrends = []
      alertTrendData.dates = []
      alertTrendData.values = []
      updateChart()
    }
  } catch (error) {
    console.error('获取报警数据失败:', error)
    stats.value.totalAlarms = 0
    stats.value.handledAlarms = 0
    stats.value.handlingRate = 0
    stats.value.alarmTrends = []
    alertTrendData.dates = []
    alertTrendData.values = []
    updateChart()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: alertTrendData.dates
    },
    series: [{
      data: alertTrendData.values
    }]
  })
}

// 定期刷新数据
const setupDataRefresh = () => {
  setInterval(async () => {
    await fetchAlertData()
  }, 5 * 60 * 1000)
}

onMounted(async () => {
  await nextTick()
  await fetchAlertData()
  initChart()
  // setupDataRefresh()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot.blue {
  background: rgba(5, 90, 219, 0.4);
}

.stat-dot.cyan {
  background: rgba(35, 202, 255, 0.4);
}

.stat-dot.green {
  background: rgba(63, 216, 124, 0.4);
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  position: absolute;
}

.stat-dot.blue .stat-dot-inner {
  background: #055ADB;
}

.stat-dot.cyan .stat-dot-inner {
  background: #23CAFF;
}

.stat-dot.green .stat-dot-inner {
  background: #3FD87C;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-red {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #FF3C3C;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #FF0000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #3CF3FF 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-green {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #36F281;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  margin-left: 3px;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 180px;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 940px) and (max-height: 1055px){
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .stats-row {
    margin-bottom: 8px;
  }
  
  .stat-item {
    gap: 3px;
  }
  
  .stat-dot {
    width: 7px;
    height: 7px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 18px;
    line-height: 20px;
  }
  
  .stat-unit {
    font-size: 10px;
    margin-left: 2px;
  }
  
  .chart-title-container {
    margin-bottom: 5px;
  }
  
  .chart-title {
    font-size: 12px;
  }
  
  .chart-wrapper {
    min-height: 150px;
  }
  
  /* 调整图表的内部属性，能在script中实现更好 */
  :deep(.echarts) {
    height: 150px !important;
  }
}

/* 910px左右高度的屏幕特别优化 */
@media (min-height: 900px) and (max-height: 939px){
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .stats-row {
    margin-bottom: 5px;
  }
  
  .stat-item {
    gap: 2px;
  }
  
  .stat-dot {
    width: 6px;
    height: 6px;
  }
  
  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-value-red,
  .stat-value-blue,
  .stat-value-green {
    font-size: 16px;
    line-height: 18px;
  }
  
  .stat-unit {
    font-size: 9px;
    margin-left: 2px;
  }
  
  .chart-title-container {
    margin-bottom: 3px;
  }
  
  .chart-title {
    font-size: 11px;
  }
  
  .chart-wrapper {
    min-height: 120px;
  }
  
  :deep(.echarts) {
    height: 120px !important;
  }
}
</style>