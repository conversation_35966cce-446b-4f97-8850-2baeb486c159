<template>
  <PanelBox title="预警处置统计" class="left-middle-panel">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item" v-for="(item, index) in legendItems" :key="index">
          <div class="legend-color" :style="{ background: item.color }"></div>
          <div class="legend-text">{{ item.name }}</div>
        </div>
      </div>
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'
import { getLinkageWarningHandleStatistics } from '@/api/comprehensive'

// 综合态势总览左中面板组件

// 管线类型选项
const pipeTypeOptions = [
  { value: 'week', label: '近一周' },
  { value: 'month', label: '近一月' },
  { value: 'year', label: '近一年' }
]

// 默认选择雨水管线
const selectedPipeType = ref('week')

// 图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null
// 加载状态
const loading = ref(false)
// 图表数据
const chartData = ref({
  categories: ['燃气', '排水', '供热', '桥梁'],
  series: [
    { name: '待处置', data: [0, 0, 0, 0] },
    { name: '处置中', data: [0, 0, 0, 0] },
    { name: '已处置', data: [0, 0, 0, 0] },
    { name: '已解除', data: [0, 0, 0, 0] }
  ]
})
 
// 图例数据
const legendItems = reactive([
  { name: '待处置', color: '#DE6970' },
  { name: '处置中', color: '#FE9150' },
  { name: '已处置', color: '#D8F115' },
  { name: '已解除', color: '#00E1B9' }
])

// 创建图表配置
const createChartOption = () => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00FFFF',
      borderWidth: 0,
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      formatter: function(params) {
        let result = `<div style="margin-bottom: 5px; font-weight: bold;">${params[0].axisValue}</div>`
        params.forEach(param => {
          const color = param.color
          result += `<div style="margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; margin-right: 5px;"></span>
            ${param.seriesName}: ${param.value}个
          </div>`
        })
        return result
      }
    },
    legend: {
      show:false,
      data: legendItems.map(item => item.name),
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 12,
      top: 10,
      right: 20
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.categories,
      axisLine: {
        lineStyle: {
          color: '#00FFFF'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 11
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: chartData.value.series.map((seriesItem, index) => ({
      name: seriesItem.name,
      type: 'bar',
      stack: 'total',
      data: seriesItem.data,
      itemStyle: {
        color: legendItems[index].color
      },
      barWidth: index === 0 ? '60%' : undefined
    }))
  }
}

/**
 * 根据时间范围类型计算开始时间和结束时间
 * @param {string} range 时间范围类型：'week'、'month'、'year'
 * @returns {object} 包含startTime和endTime的对象
 */
const getTimeRange = (range) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime
  
  switch (range) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }
  
  return { startTime, endTime }
}

/**
 * 获取联动预警处置统计数据
 * @param {string} timeRange 时间范围
 */
const fetchHandleStatistics = async (timeRange = 'week') => {
  try {
    loading.value = true
    const { startTime, endTime } = getTimeRange(timeRange)
    
    const response = await getLinkageWarningHandleStatistics({
      startTime,
      endTime
    })
    
    if (response.code === 200 && response.data) {
      processChartData(response.data)
    }
  } catch (error) {
    console.error('获取联动预警处置统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理图表数据
 * @param {Array} data API返回的数据
 */
const processChartData = (data) => {
  // 业务类型映射
  const businessMap = {
    7000501: '燃气',
    7000502: '排水',
    7000503: '供热',
    7000504: '桥梁'
  }
  
  // 初始化数据
  const categories = ['燃气', '排水', '供热', '桥梁']
  const pendingData = [0, 0, 0, 0]
  const handlingData = [0, 0, 0, 0]
  const handledData = [0, 0, 0, 0]
  const releasedData = [0, 0, 0, 0]
  
  // 处理API数据
  data.forEach(item => {
    const businessName = businessMap[item.relatedBusiness]
    const index = categories.indexOf(businessName)
    
    if (index !== -1) {
      pendingData[index] = item.pendingHandle || 0
      handlingData[index] = item.handling || 0
      handledData[index] = item.handled || 0
      releasedData[index] = item.released || 0
    }
  })
  
  // 更新图表数据
  chartData.value = {
    categories,
    series: [
      { name: '待处置', data: pendingData },
      { name: '处置中', data: handlingData },
      { name: '已处置', data: handledData },
      { name: '已解除', data: releasedData }
    ]
  }
  
  // 更新图表
  updateChart()
}



// 处理管线类型变化
const handlePipeTypeChange = () => {
  console.log(`切换到${selectedPipeType.value}`)
  fetchHandleStatistics(selectedPipeType.value)
}

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (!chartRef.value) {
    console.error('图表DOM引用不存在')
    return
  }

  console.log('初始化图表，容器大小:', chartRef.value.offsetWidth, chartRef.value.offsetHeight)

  try {
    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)

    // 设置图表配置项
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)

    console.log('图表初始化完成')
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在，无法更新')
    return
  }

  try {
    const option = createChartOption()
    chartInstance.setOption(option)
    chartInstance.resize()
    console.log('图表配置已更新')
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}



// 开始数据刷新
const startDataRefresh = () => {
  refreshTimer.value = setInterval(() => {
    if (document.visibilityState === 'visible') {
      fetchHandleStatistics(selectedPipeType.value)
    }
  }, 10 * 60 * 1000) // 10分钟刷新一次
}

// 停止数据刷新
const stopDataRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 定时器引用
const refreshTimer = ref(null)



// 组件挂载时初始化图表并获取数据
onMounted(async () => {
  console.log('组件已挂载')
  await initChart()
  fetchHandleStatistics() // 初始数据获取
  startDataRefresh() // 开始定时刷新
})

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('组件已卸载')
  stopDataRefresh() // 停止定时刷新
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.left-middle-panel {
  height: 310px;
}

.panel-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 35, 80, 0.5);
  border: 1px solid rgba(0, 242, 241, 0.2);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 242, 241, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(90deg, 
    rgba(0, 242, 241, 0.05) 0%, 
    rgba(0, 242, 241, 0.2) 50%, 
    rgba(0, 242, 241, 0.05) 100%);
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 242, 241, 0.2);
}

.panel-title {
  color: #00f2f1;
  font-size: 16px;
  margin: 0;
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.chart-container {
  flex: 1;
  min-height: 200px;
  width: 100%;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-middle-panel {
    height: 310px;
  }
  .chart-container {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .left-middle-panel {
    height: 310px;
  }
  .chart-container {
    min-height: 180px;
  }
  
  .chart-legend {
    gap: 10px;
  }
  
  .legend-text {
    font-size: 12px;
  }
}

@media screen and (min-width: 2561px) {
  .left-middle-panel {
    height: 310px;
  }
  .chart-container {
    min-height: 220px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .left-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 15px;
  }
  .chart-container {
    min-height: 220px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .left-middle-panel {
    height: 310px;
  }
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  .chart-container {
    min-height: 200px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 939px) {
  .left-middle-panel {
    height: 252px;
  }
  .panel-content {
    padding: 8px;
    gap: 5px;
  }
  
  .chart-legend {
    gap: 10px;
  }
  
  .legend-item {
    gap: 3px;
  }
  
  .legend-text {
    font-size: 12px;
  }
  
  .chart-container {
    min-height: 180px;
  }
}
</style>