<template>
  <div class="gas-leak-monitor-analysis">
    <!-- 查询表单区域 -->
    <div class="search-section">
      <GasLeakAlarmSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        height="400"
      >
        <el-table-column label="序号" width="60" align="center">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="报警来源" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource" class="text-ellipsis">
              {{ ALARM_SOURCE_MAP[scope.row.alarmSource] || scope.row.alarmSource }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmCode" label="报警编号" width="180" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.alarmCode" class="text-ellipsis">
              {{ scope.row.alarmCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmTime" label="报警时间" width="170" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.alarmTime" class="text-ellipsis">
              {{ scope.row.alarmTime }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="报警设备名称" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.deviceName" class="text-ellipsis">
              {{ scope.row.deviceName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="报警类型" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="ALARM_TYPE_MAP[scope.row.alarmType] || scope.row.alarmTypeName" class="text-ellipsis">
              {{ ALARM_TYPE_MAP[scope.row.alarmType] || scope.row.alarmTypeName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="monitorObjectName" label="监测对象" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.monitorObjectName" class="text-ellipsis">
              {{ scope.row.monitorObjectName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmValue" label="报警值" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.alarmValue" class="text-ellipsis">
              {{ scope.row.alarmValue }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmLocation" label="报警位置" min-width="150" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.alarmLocation" class="text-ellipsis">
              {{ scope.row.alarmLocation }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="报警级别" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <div :class="getAlarmLevelClass(scope.row.alarmLevel)" :title="getAlarmLevelText(scope.row.alarmLevel) || scope.row.alarmLevelName">
              {{ getAlarmLevelText(scope.row.alarmLevel) || scope.row.alarmLevelName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ownershipUnitName" label="权属单位" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.ownershipUnitName" class="text-ellipsis">
              {{ scope.row.ownershipUnitName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="报警状态" width="120" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span :title="ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName" class="text-ellipsis">
              {{ ALARM_STATUS_MAP[scope.row.alarmStatus] || scope.row.alarmStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleDeviceAnalysis(scope.row)">设备关联分析</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleGasIdentification(scope.row)">沼气-燃气辨识</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, prev, pager, next, jumper, sizes"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 报警详情弹窗 -->
    <GasLeakAnalysisDialog
      v-model:visible="dialogVisible"
      :alarm-id="currentAlarmId"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElPagination, ElMessage } from 'element-plus';
import GasLeakAlarmSearch from './components/GasLeakAlarmSearch.vue';
import GasLeakAnalysisDialog from './components/GasLeakAnalysisDialog.vue';
import { getAlarmList } from '@/api/gas';
import { ALARM_LEVEL_MAP, ALARM_STATUS_MAP, ALARM_SOURCES, ALARM_SOURCE_MAP, ALARM_TYPES, ALARM_TYPE_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 详情弹窗相关
const dialogVisible = ref(false);
const currentAlarmId = ref('');

// 获取报警等级文本
const getAlarmLevelText = (level) => {
  return ALARM_LEVEL_MAP[level] || '';
};

// 获取报警等级样式
const getAlarmLevelClass = (level) => {
  const map = {
    '9101': 'alarm-level-first',
    '9102': 'alarm-level-second',
    '9103': 'alarm-level-third',
    '9104': 'alarm-level-third'
  };
  return ['alarm-level-tag', map[level]];
};

// 处理搜索
const handleSearch = (formData) => {
  // 转换查询参数
  const params = {};
  
  if (formData.alarmSource && formData.alarmSource !== 'all') {
    // 查找对应的数值
    const source = ALARM_SOURCES.find(item => item.label === formData.alarmSource);
    if (source) {
      params.alarmSource = source.value;
    }
  }
  
  if (formData.alarmLevel && formData.alarmLevel !== 'all') {
    params.alarmLevel = parseInt(formData.alarmLevel);
  }
  
  if (formData.alarmType && formData.alarmType !== 'all') {
    // 查找对应的数值
    const type = ALARM_TYPES.find(item => item.label === formData.alarmType);
    if (type) {
      params.alarmType = type.value;
    }
  }
  
  if (formData.timeRange && formData.timeRange.length === 2) {
    params.startTime = formData.timeRange[0];
    params.endTime = formData.timeRange[1];
  }
  
  if (formData.alarmStatus && formData.alarmStatus !== 'all') {
    // 转换状态为对应的数值
    const statusMap = {
      'pending': 9201, // 待确认
      'processing': 9204, // 处置中
      'completed': 9205 // 已处置
    };
    params.alarmStatus = statusMap[formData.alarmStatus];
  }
  
  if (formData.code) {
    params.code = formData.code;
  }
  
  queryParams.value = params;
  fetchAlarmData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchAlarmData();
};

// 获取报警数据
const fetchAlarmData = async () => {
  try {
    const res = await getAlarmList(currentPage.value, pageSize.value, queryParams.value);
    if (res.code === 200 && res.data) {
      tableData.value = res.data.rows || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取报警数据失败:', error);
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchAlarmData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAlarmData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleDeviceAnalysis = (row) => {
  currentAlarmId.value = row.id;
  dialogVisible.value = true;
  // 在弹窗打开后切换到设备关联分析标签页
  setTimeout(() => {
    // 假设子组件中有一个方法可以切换标签页
    // 这个可能需要在子组件中暴露出来
    const dialog = document.querySelector('.gas-leak-analysis-dialog');
    if (dialog) {
      const tabs = dialog.querySelector('.el-tabs');
      if (tabs) {
        const deviceAnalysisTab = tabs.querySelector('[aria-controls="pane-deviceAnalysis"]');
        if (deviceAnalysisTab) {
          deviceAnalysisTab.click();
        }
      }
    }
  }, 300);
};

const handleGasIdentification = (row) => {
  currentAlarmId.value = row.id;
  dialogVisible.value = true;
  // 在弹窗打开后切换到沼气-燃气辨识标签页
  setTimeout(() => {
    // 假设子组件中有一个方法可以切换标签页
    const dialog = document.querySelector('.gas-leak-analysis-dialog');
    if (dialog) {
      const tabs = dialog.querySelector('.el-tabs');
      if (tabs) {
        const gasIdentificationTab = tabs.querySelector('[aria-controls="pane-gasIdentification"]');
        if (gasIdentificationTab) {
          gasIdentificationTab.click();
        }
      }
    }
  }, 300);
};

const handleDetail = (row) => {
  currentAlarmId.value = row.id + "";
  dialogVisible.value = true;
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

onMounted(() => {
  fetchAlarmData();
});
</script>

<style scoped>
.gas-leak-monitor-analysis {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #F0F2F5;
  gap: 16px;
}

/* 搜索表单区域 */
.search-section {
  background: #FFFFFF;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 表格区域样式 */
.table-section {
  width: 100%;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
}

/* 表格样式 */
:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F8FA;
  height: 500px;
}

:deep(.el-table__inner-wrapper),
:deep(.el-scrollbar),
:deep(.el-scrollbar__wrap) {
  height: 400px !important;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #FFFFFF;
}

:deep(.el-table .odd-row) {
  background-color: #F5F8FA;
}

:deep(.el-table th) {
  background-color: #F5F8FA;
  color: #333333;
  font-weight: bold;
  height: 40px;
  padding: 0;
  border-bottom: 1px solid #EBEEF5;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

/* 报警等级标签样式 */
.alarm-level-tag {
  width: 72px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 0 auto;
}

.alarm-level-first {
  background: rgba(255,0,0,0.1);
  border: 1px solid #FF0000;
  color: #FF0000;
}

.alarm-level-second {
  background: rgba(255,133,0,0.1);
  border: 1px solid #FF8500;
  color: #FF8500;
}

.alarm-level-third {
  background: rgba(255,211,0,0.1);
  border: 1px solid #FFD300;
  color: #FFD300;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #EBEEF5;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 16px;
}

:deep(.el-pagination .el-input__wrapper) {
  box-shadow: 0 0 0 1px #CED3DA inset;
}

:deep(.el-pagination .el-select .el-input) {
  margin: 0 8px;
}

:deep(.el-pagination button:disabled) {
  background-color: #F5F8FA;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 16px;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本溢出省略号样式 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 100%;
}
</style>