<template>
  <PanelBox title="区域管网风险" class="left-bottom-panel">
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item" v-for="(item, index) in legendItems" :key="index">
          <div class="legend-color" :style="{ background: item.color }"></div>
          <div class="legend-text">{{ item.name }}</div>
        </div>
      </div>
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import * as echarts from 'echarts'
import { getPipelineRiskRegionStatistics } from '@/api/gas'

// 图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null

// 图例数据
const legendItems = reactive([
  { name: '重大风险', color: '#FF2E01' },
  { name: '较大风险', color: '#FF8200' },
  { name: '一般风险', color: '#F4DD70' },
  { name: '低风险', color: '#40CDFF' }
])

// 图表数据
const chartData = reactive({
  xAxisData: [],
  series: [
    {
      name: '低风险',
      data: [],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#40CDFF' },
          { offset: 1, color: 'rgba(0,59,89,0.01)' }
        ]
      }
    },
    {
      name: '一般风险',
      data: [],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#F4DD70' },
          { offset: 1, color: 'rgba(59,59,2,0.01)' }
        ]
      }
    },
    {
      name: '较大风险',
      data: [],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FF954C' },
          { offset: 1, color: 'rgba(89,49,0,0.01)' }
        ]
      }
    },
    {
      name: '重大风险',
      data: [],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FF7066' },
          { offset: 1, color: 'rgba(89,8,0,0.01)' }
        ]
      }
    }
  ]
})

// 初始化图表
const initChart = async () => {
  await nextTick()
  if (!chartRef.value) {
    console.error('图表DOM引用不存在')
    return
  }

  try {
    chartInstance = echarts.init(chartRef.value)
    updateChart()
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在，无法更新')
    return
  }

  try {
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        confine: true,
        position: function (point, params, dom, rect, size) {
          // 确保tooltip不会被遮挡
          return [point[0] + 10, point[1] - 50]
        },
      },
      grid: {
        top: '16%',
        left: '8%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#5F5F60'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '单位（公里）',
        nameTextStyle: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14,
          padding: [0, 30, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 14
        }
      },
      series: chartData.series.map((item) => {
        return {
          name: item.name,
          type: 'bar',
          barWidth: '10px',
          barGap: '30%',
          emphasis: {
            focus: 'series'
          },
          data: item.data,
          itemStyle: {
            color: item.color
          }
        }
      })
    }

    chartInstance.setOption(option)
    chartInstance.resize()
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}

// 从后端获取数据
const fetchData = async () => {
  try {
    const res = await getPipelineRiskRegionStatistics()
    if (res.code === 200) {
      const regionData = res.data.regionRiskStatistics

      // 过滤掉空区域名称的数据
      const validRegionData = regionData.filter(item => item.regionName)

      // 更新x轴数据
      chartData.xAxisData = validRegionData.map(item => item.regionName)

      // 更新系列数据
      chartData.series[0].data = validRegionData.map(item => item.lowRiskLength)
      chartData.series[1].data = validRegionData.map(item => item.generalRiskLength)
      chartData.series[2].data = validRegionData.map(item => item.largerRiskLength)
      chartData.series[3].data = validRegionData.map(item => item.bigRiskLength)

      updateChart()
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 组件挂载时初始化图表并获取数据
onMounted(async () => {
  await fetchData()
  await initChart()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.left-bottom-panel {
  height: 320px;
  /* 默认高度为320px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 30px);
  /* 减去padding的高度 */
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 4px;
}

.legend-text {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
}

.chart-container {
  flex: 1;
  min-height: 200px;
  /* 设置最小高度 */
  width: 100%;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-bottom-panel {
    height: 320px;
  }
}

@media screen and (max-width: 1919px) {
  .left-bottom-panel {
    height: 300px;
  }
}

@media screen and (min-width: 2561px) {
  .left-bottom-panel {
    height: 350px;
  }
}

/* 910px高度的屏幕特别优化 */
@media screen and (min-height: 900px) and (max-height: 939px) {
  .left-bottom-panel {
    height: 280px;
  }

  .panel-content {
    padding: 8px;
    gap: 5px;
  }

  .chart-legend {
    gap: 10px;
  }

  .legend-item {
    gap: 3px;
  }

  .legend-text {
    font-size: 12px;
  }

  .chart-container {
    min-height: 180px;
  }
}
</style>