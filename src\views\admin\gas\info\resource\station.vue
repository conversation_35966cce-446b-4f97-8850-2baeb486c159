<template>
  <div class="gas-info-resource-station">
    <!-- 搜索区域 -->
    <div class="search-section">
      <GasStationSearch @search="handleSearch" @reset="handleReset" />
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container" ref="tableContainerRef">
      <el-table
        ref="tableRef"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        :height="tableHeight"
        :scrollbar-always-on="true"
        :fit="false"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="场站名称" min-width="120" />
        <el-table-column prop="stationName" label="场站类型" min-width="100" />
        <el-table-column prop="distributionMedium" label="分输介质" min-width="100" />
        <el-table-column prop="designCapacity" label="设计供气能力(立方米/日)" min-width="180" />
        <el-table-column prop="totalTankVolume" label="总储罐容量" min-width="120" />
        <el-table-column prop="tankCount" label="储罐数量" min-width="100" />
        <el-table-column prop="tankTypeName" label="储罐类型" min-width="170" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.tankTypeName" class="text-ellipsis">
              {{ scope.row.tankTypeName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="tankVolume" label="储罐容量" min-width="120" />
        <el-table-column prop="tankPressureLevel" label="储罐压力等级" min-width="120" />
        <el-table-column prop="operationTime" label="投入运行时间" min-width="170" />
        <el-table-column prop="address" label="位置" min-width="120" />
        <el-table-column prop="managementUnit" label="权属单位" min-width="220" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.managementUnit" class="text-ellipsis">
              {{ scope.row.managementUnit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 弹窗组件 -->
    <GasStationDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox,ElMessage } from 'element-plus';
import GasStationSearch from './components/GasStationSearch.vue';
import GasStationDialog from './components/GasStationDialog.vue';
import { getGasStationPage, getGasStationDetail, deleteGasStation } from '@/api/gas';
import { STATION_TYPE_MAP, TANK_TYPE_MAP } from '@/constants/gas';
import { misPosition } from '@/hooks/gishooks';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 表格refs和动态高度
const tableRef = ref(null);
const tableContainerRef = ref(null);
const tableHeight = ref(400);

// 查询参数
const queryParams = ref({});

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  currentPage.value = 1;
  fetchStationData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchStationData();
};

// 获取场站数据
const fetchStationData = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };
    const res = await getGasStationPage(params);
    if (res && res.code === 200) {
      tableData.value = res.data.records.map(item => ({
        ...item,
        stationType: STATION_TYPE_MAP[item.stationType] || item.stationType,
        tankType: TANK_TYPE_MAP[item.tankType] || item.tankType
      }));
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取场站数据失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchStationData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchStationData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

const handleImport = () => {
  console.log('导入');
};

const handleExport = () => {
  console.log('导出');
};

const handleEdit = async (row) => {
  try {
    const res = await getGasStationDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取场站详情失败', error);
  }
};

const handleDetail = async (row) => {
  try {
    const res = await getGasStationDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取场站详情失败', error);
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该场站吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteGasStation(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchStationData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除场站失败', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 弹窗提交成功回调
const handleDialogSuccess = () => {
  fetchStationData();
};

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainerRef.value) {
      // 获取容器的实际高度，表格应该填充整个容器
      const containerHeight = tableContainerRef.value.offsetHeight;
      // 直接使用容器高度，不需要预留空间，因为容器已经通过flex布局确定了正确高度
      tableHeight.value = Math.max(containerHeight, 200);
    }
  });
};

// 设置滚动同步
const setupScrollSync = () => {
  nextTick(() => {
    if (tableRef.value) {
      const tableElement = tableRef.value.$el;
      const headerWrapper = tableElement.querySelector('.el-table__header-wrapper');
      const bodyWrapper = tableElement.querySelector('.el-table__body-wrapper');
      
      if (headerWrapper && bodyWrapper) {
        // 隐藏表头的水平滚动条
        headerWrapper.style.overflowX = 'hidden';
        
        // 监听表体滚动事件
        const handleScroll = () => {
          headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
        };
        
        bodyWrapper.addEventListener('scroll', handleScroll);
        
        // 保存清理函数
        window.tableScrollCleanup = () => {
          bodyWrapper.removeEventListener('scroll', handleScroll);
        };
      }
    }
  });
};

// 监听窗口大小变化
const handleResize = () => {
  calculateTableHeight();
};

onMounted(() => {
  fetchStationData();
  calculateTableHeight();
  setupScrollSync();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (window.tableScrollCleanup) {
    window.tableScrollCleanup();
    delete window.tableScrollCleanup;
  }
});
</script>

<style scoped>
.gas-info-resource-station {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 - 自适应高度 */
.table-container {
  flex: 1;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* 自定义滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}


/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>