<template>
  <PanelBox title="报警趋势分析">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="chart-legend">
        <div class="legend-item">
          <span class="legend-icon gas"></span>
          <span class="legend-text">燃气</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon drain"></span>
          <span class="legend-text">排水</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon heat"></span>
          <span class="legend-text">供热</span>
        </div>
        <div class="legend-item">
          <span class="legend-icon bridge"></span>
          <span class="legend-text">桥梁</span>
        </div>
      </div>
      <div class="chart-title-container">
        <span class="unit-label">单位（个）</span>
      </div>
      <div class="chart-wrapper" ref="chartRef"></div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import moment from 'moment'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getAlarmTrendStatistics } from '@/api/comprehensive'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 图表DOM引用
const chartRef = ref(null)
let chartInstance = null

// 预设颜色
const colors = {
  gas: '#FF311D',      // 燃气 - 红色
  drain: '#055ADB',    // 排水 - 蓝色
  heat: '#FF6817',     // 供热 - 橙色
  bridge: '#FFD32E'    // 桥梁 - 黄色
}

// 图表数据
const chartData = ref({
  xAxis: [],
  series: [
    {
      name: '燃气',
      data: [],
      color: colors.gas
    },
    {
      name: '排水',
      data: [],
      color: colors.drain
    },
    {
      name: '供热',
      data: [],
      color: colors.heat
    },
    {
      name: '桥梁',
      data: [],
      color: colors.bridge
    }
  ]
})

// 加载状态
const loading = ref(false)

/**
 * 根据时间范围计算开始和结束时间
 * @param {string} range 时间范围：week/month/year
 * @returns {object} 包含startTime和endTime的对象
 */
const getTimeRange = (range) => {
  const now = moment()
  let startTime, endTime
  
  endTime = now.format('YYYY-MM-DD HH:mm:ss')
  
  switch (range) {
    case 'week':
      startTime = now.subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = now.subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = now.subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = now.subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }
  
  return { startTime, endTime }
}

/**
 * 获取报警趋势数据
 */
const fetchAlarmTrendData = async () => {
  try {
    loading.value = true
    const { startTime, endTime } = getTimeRange(timeRange.value)
    
    const response = await getAlarmTrendStatistics({ startTime, endTime })
    
    if (response && response.data) {
      processChartData(response.data)
    }
  } catch (error) {
    console.error('获取报警趋势数据失败:', error)
    // 发生错误时使用空数据
    chartData.value.xAxis = []
    chartData.value.series.forEach(series => {
      series.data = []
    })
  } finally {
    loading.value = false
  }
}

/**
 * 处理API返回的数据，转换为图表所需格式
 * @param {object} data API返回的数据
 */
const processChartData = (data) => {
  const { gasTrendResponse = [], drainTrendResponse = [], heatTrendResponse = [], bridgeTrendResponse = [] } = data
  
  // 收集所有日期并排序
  const allDates = new Set()
  gasTrendResponse.forEach(item => allDates.add(item.date))
  drainTrendResponse.forEach(item => allDates.add(item.date))
  heatTrendResponse.forEach(item => allDates.add(item.date))
  bridgeTrendResponse.forEach(item => allDates.add(item.date))
  
  const sortedDates = Array.from(allDates).sort()
  
  // 格式化X轴标签
  const xAxisData = sortedDates.map(date => {
    if (timeRange.value === 'week') {
      return moment(date).format('MM-DD')
    } else if (timeRange.value === 'month') {
      return moment(date).format('MM-DD')
    } else {
      return moment(date).format('YYYY-MM')
    }
  })
  
  // 创建数据映射
  const createDataMap = (trendData) => {
    const map = {}
    trendData.forEach(item => {
      map[item.date] = item.totalCount || 0
    })
    return map
  }
  
  const gasMap = createDataMap(gasTrendResponse)
  const drainMap = createDataMap(drainTrendResponse)
  const heatMap = createDataMap(heatTrendResponse)
  const bridgeMap = createDataMap(bridgeTrendResponse)
  
  // 生成各业务线数据
  const gasData = sortedDates.map(date => gasMap[date] || 0)
  const drainData = sortedDates.map(date => drainMap[date] || 0)
  const heatData = sortedDates.map(date => heatMap[date] || 0)
  const bridgeData = sortedDates.map(date => bridgeMap[date] || 0)
  
  // 更新图表数据
   chartData.value.xAxis = xAxisData
   chartData.value.series[0].data = gasData
   chartData.value.series[1].data = drainData
   chartData.value.series[2].data = heatData
   chartData.value.series[3].data = bridgeData
   
   // 更新图表显示
   updateChart()
 }

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchAlarmTrendData()
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '18%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.7)',
      borderColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxis,
      axisLine: {
        lineStyle: {
          color: '#5F5F60',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12,
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 12
      }
    },
    series: [
      {
        name: '燃气',
        type: 'line',
        data: chartData.value.series[0].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.gas
        },
        lineStyle: {
          color: colors.gas,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 49, 29, 0.5)' },
              { offset: 1, color: 'rgba(255, 49, 29, 0)' }
            ]
          }
        }
      },
      {
        name: '排水',
        type: 'line',
        data: chartData.value.series[1].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.drain
        },
        lineStyle: {
          color: colors.drain,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(5, 90, 219, 0.5)' },
              { offset: 1, color: 'rgba(5, 90, 219, 0)' }
            ]
          }
        }
      },
      {
        name: '供热',
        type: 'line',
        data: chartData.value.series[2].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.heat
        },
        lineStyle: {
          color: colors.heat,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 104, 23, 0.5)' },
              { offset: 1, color: 'rgba(255, 104, 23, 0)' }
            ]
          }
        }
      },
      {
        name: '桥梁',
        type: 'line',
        data: chartData.value.series[3].data,
        smooth: false,
        symbol: 'none',
        itemStyle: {
          color: colors.bridge
        },
        lineStyle: {
          color: colors.bridge,
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 211, 46, 0.5)' },
              { offset: 1, color: 'rgba(255, 211, 46, 0)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return

  chartInstance.setOption({
    xAxis: {
      data: chartData.value.xAxis
    },
    series: [
      { data: chartData.value.series[0].data },
      { data: chartData.value.series[1].data },
      { data: chartData.value.series[2].data },
      { data: chartData.value.series[3].data }
    ]
  })
}

// 处理窗口大小调整
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  // 初始化图表
  initChart()
  // 获取初始数据
  fetchAlarmTrendData()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.com-select {
  margin-right: 20px;
}

.chart-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 20px;
}

.unit-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  position: absolute;
  left: 0;
}

.chart-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  padding: 1px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-icon {
  width: 10px;
  height: 2px;
  border-radius: 2px;
}

.legend-icon.gas {
  background: #FF311D;
}

.legend-icon.drain {
  background: #055ADB;
}

.legend-icon.heat {
  background: #FF6817;
}

.legend-icon.bridge {
  background: #FFD32E;
}

.legend-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  min-height: 150px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 200px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .chart-wrapper {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .chart-wrapper {
    min-height: 240px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
  
  .chart-wrapper {
    min-height: 210px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 10px;
  }
  
  .chart-wrapper {
    min-height: 190px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 8px;
  }
  
  .chart-wrapper {
    min-height: 170px;
  }
}
</style>