<template>
  <div class="gas-network-maintenance">
    <!-- 维修弹窗 -->
    <GasNetworkMaintenanceDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentRow"
      @success="handleDialogSuccess"
    />
    <!-- 搜索区域 -->
    <div class="search-section">
      <GasMaintenanceSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container" ref="tableContainer">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        :height="tableHeight"
        :fit="false"
        :scrollbar-always-on="true"
        style="width: 100%"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="repairCode" label="维修单号" min-width="120" />
        <el-table-column prop="connectedPipeline" label="关联管线" min-width="120" />
        <el-table-column prop="repairTypeName" label="维修类型" min-width="100" />
        <el-table-column prop="repairDesc" label="维修内容" min-width="150" />
        <el-table-column prop="attachedFacilities" label="附属设施" min-width="120" />
        <el-table-column prop="repairResult" label="维修结果" min-width="120" />
        <el-table-column prop="repairTime" label="维修时间" min-width="170" />
        <el-table-column prop="repairUser" label="维修人" min-width="100" />
        <el-table-column prop="address" label="维修位置" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.address" class="text-ellipsis">
              {{ scope.row.address }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span>
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 - 固定在底部 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElMessageBox } from 'element-plus';
import GasMaintenanceSearch from './GasMaintenanceSearch.vue';
import GasNetworkMaintenanceDialog from './GasNetworkMaintenanceDialog.vue';
import { getGasRepairPage, deleteGasRepair } from '@/api/gas';
import { misPosition } from '@/hooks/gishooks' //地图定位

// 表格高度相关
const tableContainer = ref(null);
const tableHeight = ref(400);

// 表格横向滚动同步
const setupScrollSync = () => {
  nextTick(() => {
    if (tableContainer.value) {
      const headerWrapper = tableContainer.value.querySelector('.el-table__header-wrapper');
      const bodyWrapper = tableContainer.value.querySelector('.el-table__body-wrapper');
      
      if (headerWrapper && bodyWrapper) {
        // 监听表体横向滚动，同步表头滚动
        bodyWrapper.addEventListener('scroll', (e) => {
          headerWrapper.scrollLeft = e.target.scrollLeft;
        });
      }
    }
  });
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainer.value) {
      // 获取容器的实际高度
      const containerHeight = tableContainer.value.offsetHeight;
      // 设置表格高度，预留一些空间给滚动条
      tableHeight.value = Math.max(containerHeight - 10, 300);
    }
  });
};

// 处理搜索
const handleSearch = (formData) => {
  queryParams.value = formData;
  fetchMaintenanceData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  fetchMaintenanceData();
};

// 获取维修数据
const fetchMaintenanceData = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    };
    const response = await getGasRepairPage(params);
    if (response && response.code === 200) {
      tableData.value = response.data.records || [];
      total.value = response.data.total || 0;
    } else {
      ElMessage.error(response?.message || '获取数据失败');
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取维修数据失败:', error);
    ElMessage.error('获取数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  fetchMaintenanceData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchMaintenanceData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹和选中行样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
// 弹窗相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const currentRow = ref(null);

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  currentRow.value = {};
  dialogVisible.value = true;
};

// 处理导入
const handleImport = () => {
  ElMessage.info('导入功能开发中');
};

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中');
};

// 处理编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit';
  currentRow.value = { ...row };
  dialogVisible.value = true;
};

// 处理详情
const handleDetail = (row) => {
  dialogMode.value = 'view';
  currentRow.value = { ...row };
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该维修记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteGasRepair(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchMaintenanceData();
      } else {
        ElMessage.error(res?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理弹窗操作成功
const handleDialogSuccess = () => {
  fetchMaintenanceData();
};

onMounted(() => {
  fetchMaintenanceData();
  calculateTableHeight();
  setupScrollSync();
  
  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight);
});

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight);
});
</script>

<style scoped>
.gas-network-maintenance {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 - 自适应高度 */
.table-container {
  flex: 1;
  width: 100%;
  min-height: 300px;
  margin-bottom: 16px;
  overflow: hidden;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  flex-shrink: 0;
  min-height: 32px;
}

:deep(.el-table) {
  height: 100% !important;
}

/* 隐藏表头横向滚动条，但保持内容可以滚动 */
:deep(.el-table__header-wrapper) {
  overflow-x: hidden !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none; /* Chrome, Safari, Opera */
}

/* 优化表体滚动条样式 */
:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  overflow-x: auto !important;
  scrollbar-width: thin; /* Firefox */
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #909399;
}

/* 确保表头和表体水平滚动同步 */
:deep(.el-table__body-wrapper) {
  position: relative;
}

/* 当表体横向滚动时，通过 JavaScript 同步表头滚动 */

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  margin-top: 16px;
  min-height: 32px;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 防止表格过长时遮挡分页组件 */
:deep(.el-scrollbar__wrap) {
  overflow-x: auto !important;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  height: 6px !important;
  border-radius: 3px;
}

:deep(.el-scrollbar__bar.is-vertical) {
  width: 6px !important;
  border-radius: 3px;
}

:deep(.el-scrollbar__thumb) {
  background-color: #c0c4cc;
  border-radius: 3px;
}

:deep(.el-scrollbar__thumb:hover) {
  background-color: #909399;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>