<template>
  <PanelBox title="报警等级">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <!-- 报警等级统计区域 -->
      <div class="alarm-level-stats">
        <div class="alarm-level-item level-one">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">一级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level1.count }}</span>
              <span class="percent">{{ alarmStats.level1.percent }}</span>
            </div>
          </div>
        </div>

        <div class="alarm-level-item level-two">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">二级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level2.count }}</span>
              <span class="percent">{{ alarmStats.level2.percent }}</span>
            </div>
          </div>
        </div>

        <div class="alarm-level-item level-three">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">三级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level3.count }}</span>
              <span class="percent">{{ alarmStats.level3.percent }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 报警柱状图区域 -->
      <div class="chart-container" ref="chartRef"></div>

      <!-- 图例 -->
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-color level1-color"></div>
          <div class="legend-text">一级报警</div>
        </div>
        <div class="legend-item">
          <div class="legend-color level2-color"></div>
          <div class="legend-text">二级报警</div>
        </div>
        <div class="legend-item">
          <div class="legend-color level3-color"></div>
          <div class="legend-text">三级报警</div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import * as echarts from 'echarts'
import moment from 'moment'
import { getAlarmLevelStatistics } from '@/api/comprehensive'

// 综合运行监测右上面板组件

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 加载状态
const loading = ref(false)



// 报警等级统计数据
const alarmStats = ref({
  level1: { count: 0, percent: '0%' },
  level2: { count: 0, percent: '0%' },
  level3: { count: 0, percent: '0%' }
})

// 获取时间范围
const getTimeRange = (type) => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  let startTime
  
  switch (type) {
    case 'week':
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    case 'year':
      startTime = moment().subtract(365, 'days').format('YYYY-MM-DD HH:mm:ss')
      break
    default:
      startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  }
  
  return { startTime, endTime }
}

// 获取报警等级统计数据
const fetchAlarmLevelData = async (timeType = 'week') => {
  loading.value = true
  try {
    const { startTime, endTime } = getTimeRange(timeType)
    const response = await getAlarmLevelStatistics({ startTime, endTime })
    
    if (response.code === 200 && response.data) {
      const { level1Count, level2Count, level3Count, statistics } = response.data
      const total = level1Count + level2Count + level3Count
      
      // 更新统计数据
      alarmStats.value = {
        level1: { 
          count: level1Count, 
          percent: total > 0 ? ((level1Count / total) * 100).toFixed(2) + '%' : '0%' 
        },
        level2: { 
          count: level2Count, 
          percent: total > 0 ? ((level2Count / total) * 100).toFixed(2) + '%' : '0%' 
        },
        level3: { 
          count: level3Count, 
          percent: total > 0 ? ((level3Count / total) * 100).toFixed(2) + '%' : '0%' 
        }
      }
      
      // 更新图表数据
      if (statistics && statistics.length > 0) {
        updateChartData(statistics)
      }
    }
  } catch (error) {
    console.error('获取报警等级统计数据失败:', error)
    // 发生错误时重置数据
    alarmStats.value = {
      level1: { count: 0, percent: '0%' },
      level2: { count: 0, percent: '0%' },
      level3: { count: 0, percent: '0%' }
    }
  } finally {
    loading.value = false
  }
}

// 更新图表数据
const updateChartData = (statistics) => {
  const businessMap = {
    7000501: '燃气',
    7000502: '排水', 
    7000503: '供热',
    7000504: '桥梁'
  }
  
  const xAxisData = ['燃气', '排水', '供热', '桥梁']
  const level1Data = [0, 0, 0, 0]
  const level2Data = [0, 0, 0, 0]
  const level3Data = [0, 0, 0, 0]
  
  statistics.forEach(item => {
    const businessName = businessMap[item.relatedBusiness]
    const index = xAxisData.indexOf(businessName)
    if (index !== -1) {
      level1Data[index] = item.level1Count || 0
      level2Data[index] = item.level2Count || 0
      level3Data[index] = item.level3Count || 0
    }
  })
  
  currentChartData.value = {
    xAxisData,
    series: [
      {
        name: '一级报警',
        data: level1Data,
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#FF2929' },
            { offset: 1, color: 'rgba(255, 41, 41, 0.1)' }
          ]
        }
      },
      {
        name: '二级报警',
        data: level2Data,
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#FF8A14' },
            { offset: 1, color: 'rgba(255, 138, 20, 0.1)' }
          ]
        }
      },
      {
        name: '三级报警',
        data: level3Data,
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#FFFA00' },
            { offset: 1, color: 'rgba(255, 250, 0, 0.1)' }
          ]
        }
      }
    ]
  }
  
  // 更新图表
  updateChart()
}

// 图表DOM引用
const chartRef = ref(null)
// 图表实例
let chartInstance = null

// 当前展示的图表数据
const currentChartData = ref({
  xAxisData: ['燃气', '排水', '供热', '桥梁'],
  series: [
    {
      name: '一级报警',
      data: [0, 0, 0, 0],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FF2929' },
          { offset: 1, color: 'rgba(255, 41, 41, 0.1)' }
        ]
      }
    },
    {
      name: '二级报警',
      data: [0, 0, 0, 0],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FF8A14' },
          { offset: 1, color: 'rgba(255, 138, 20, 0.1)' }
        ]
      }
    },
    {
      name: '三级报警',
      data: [0, 0, 0, 0],
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: '#FFFA00' },
          { offset: 1, color: 'rgba(255, 250, 0, 0.1)' }
        ]
      }
    }
  ]
})

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  // 获取新的数据
  fetchAlarmLevelData(value)
}

// 初始化图表
const initChart = async () => {
  await nextTick()

  // 添加额外的延迟确保DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 100))

  if (!chartRef.value) {
    console.error('图表DOM引用不存在')
    return
  }

  try {
    // 确保容器有尺寸
    if (chartRef.value.offsetWidth === 0 || chartRef.value.offsetHeight === 0) {
      console.error('图表容器尺寸为0，无法初始化')
      return
    }

    // 创建图表实例
    chartInstance = echarts.init(chartRef.value)

    // 设置图表配置项
    updateChart()

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.error('图表实例不存在，无法更新')
    return
  }

  try {
    // 构建图表配置
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        top: '30%',
        left: '8%',
        right: '4%',
        bottom: '21%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: currentChartData.value.xAxisData,
        axisLine: {
          lineStyle: {
            color: '#5F5F60'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 12,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '单位（个）',
        nameTextStyle: {
          color: "rgba(255, 255, 255, 0.6)",
          fontSize: 12,
          padding: [0, 30, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontFamily: 'PingFangSC, PingFang SC',
          fontWeight: 400,
          fontSize: 12
        }
      },
      series: currentChartData.value.series.map((item) => {
        return {
          name: item.name,
          type: 'bar',
          barWidth: '16px',
          barGap: '30%',
          emphasis: {
            focus: 'series'
          },
          data: item.data,
          itemStyle: {
            color: item.color
          }
        }
      })
    }

    // 设置图表配置
    chartInstance.setOption(option)
    chartInstance.resize() // 强制重新计算大小
  } catch (error) {
    console.error('更新图表失败:', error)
  }
}

// 组件挂载时初始化图表和数据
onMounted(() => {
  // 使用setTimeout延迟初始化，确保DOM已完全渲染
  setTimeout(() => {
    initChart()
    // 初始化数据
    fetchAlarmLevelData(timeRange.value)
  }, 300)
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.com-select {
  margin-right: 20px;
}

/* 报警等级统计样式 */
.alarm-level-stats {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  gap: 16px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.alarm-level-item {
  position: relative;
  width: 138px;
  height: 60px;
  border: 1px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.level-one {
  border-color: #FF2626;
  background: linear-gradient(90deg, rgba(255, 38, 38, 0.2) 0%, rgba(255, 38, 38, 0.05) 100%);
}

.level-two {
  border-color: #FF8127;
  background: linear-gradient(90deg, rgba(255, 129, 39, 0.2) 0%, rgba(255, 129, 39, 0.05) 100%);
}

.level-three {
  border-color: #FFD700;
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.05) 100%);
}

.corner-marks {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.corner-mark {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: transparent;
}

.level-one .corner-mark {
  border-color: #FF2626;
}

.level-two .corner-mark {
  border-color: #FF8127;
}

.level-three .corner-mark {
  border-color: #FFD700;
}

.corner-mark.top-left {
  top: -1px;
  left: -1px;
  border-top: 2px solid;
  border-left: 2px solid;
}

.corner-mark.top-right {
  top: -1px;
  right: -1px;
  border-top: 2px solid;
  border-right: 2px solid;
}

.corner-mark.bottom-left {
  bottom: -1px;
  left: -1px;
  border-bottom: 2px solid;
  border-left: 2px solid;
}

.corner-mark.bottom-right {
  bottom: -1px;
  right: -1px;
  border-bottom: 2px solid;
  border-right: 2px solid;
}

.alarm-level-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.alarm-level-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  margin-bottom: 4px;
  text-align: center;
}

.alarm-level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 图表容器 */
.chart-container {
  flex: 1;
  min-height: 180px;
  width: 100%;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  position: absolute;
  top: 49%;
  right: 3%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 12px;
  height: 4px;
}

.level1-color {
  background-color: #FF2929;
}

.level2-color {
  background-color: #FF8A14;
}

.level3-color {
  background-color: #FFFA00;
}

.legend-text {
  font-family: 'PingFangSC, PingFang SC';
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }

  .alarm-level-item {
    width: 120px;
    height: 55px;
  }

  .alarm-level-title {
    font-size: 14px;
  }

  .alarm-level-value {
    font-size: 16px;
  }

  .chart-container {
    min-height: 160px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }

  .alarm-level-item {
    width: 150px;
    height: 70px;
  }

  .alarm-level-title {
    font-size: 18px;
  }

  .alarm-level-value {
    font-size: 20px;
  }

  .chart-container {
    min-height: 200px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }

  .chart-container {
    min-height: 200px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 5px;
  }

  .alarm-level-stats {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .chart-container {
    min-height: 180px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
    gap: 2px;
  }

  .alarm-level-stats {
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 2px 0;
  }

  .alarm-level-item {
    width: 110px;
    height: 50px;
  }

  .alarm-level-title {
    font-size: 12px;
  }

  .alarm-level-value {
    font-size: 14px;
    gap: 4px;
  }

  .chart-container {
    min-height: 150px;
  }

  .legend-text {
    font-size: 11px;
  }
}
</style>