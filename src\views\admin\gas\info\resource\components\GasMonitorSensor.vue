<template>
  <div class="gas-monitor-sensor">
    <!-- 搜索区域 -->
    <div class="search-section">
      <DeviceSearch @search="handleSearch" @reset="handleReset" />
    </div>

    <!-- 按钮区域 -->
    <div class="table-header" style="display: none;">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container" ref="tableContainerRef">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        :height="tableHeight"
        :scrollbar-always-on="true"
        :fit="false"
        v-loading="loading"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="180" />
        <el-table-column prop="indexCode" label="设备编码" min-width="270" />
        <el-table-column prop="monitorIndexName" label="监测指标" min-width="220" show-overflow-tooltip>
          <template #default="scope">
            <span :title="scope.row.monitorIndexName" class="text-ellipsis">
              {{ scope.row.monitorIndexName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="collectFrequency" label="采集频率" min-width="100" >
         <template #default="{ row }">
            {{ row.collectFrequency ? `${row.collectFrequency}分钟/次` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="monitorObjectName" label="监测对象" min-width="100" />
        <el-table-column prop="address" label="位置" show-overflow-tooltip min-width="380">
          <template #default="scope">
            <span :title="scope.row.address" class="text-ellipsis">
              {{ scope.row.address }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="onlineStatus" label="设备状态" min-width="80">
          <template #default="{ row }">
            <span v-if="row.onlineStatus === 1" style="color:#0086FF ;">在线</span>
            <span v-else style="color: gray;">离线</span>
        </template>
        </el-table-column>
        <el-table-column label="操作" min-width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="operation-btns">
              <div class="operation-btn-row">
                <!-- <span class="operation-btn-text" @click.stop="handleEdit(scope.row)">编辑</span>
                <span class="operation-divider">|</span> -->
                <span class="operation-btn-text" @click.stop="handleDetail(scope.row)">详情</span>
                <span class="operation-divider">|</span>
                <!-- <span class="operation-btn-text" @click.stop="handleDelete(scope.row)">删除</span>
                <span class="operation-divider">|</span> -->
                <span class="operation-btn-text" @click.stop="handleLocation(scope.row)">定位</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :pager-count="5"
      />
    </div>

    <!-- 设备信息对话框 -->
    <GasMonitorSensorDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="currentRow"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import DeviceSearch from './DeviceSearch.vue';
import GasMonitorSensorDialog from './GasMonitorSensorDialog.vue';
import { getSensorDevicePage, getSensorDeviceDetail, deleteSensorDevice } from '@/api/gas';
import { misPosition } from '@/hooks/gishooks';

// 加载状态
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 查询参数
const queryParams = ref({});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const currentRow = ref({});

// 表格高度相关
const tableContainerRef = ref(null);
const tableHeight = ref(400);

// 获取传感器数据
const fetchSensorData = async () => {
  loading.value = true;
  try {
    const res = await getSensorDevicePage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...queryParams.value
    });
    
    if (res && (res.code === 200 || res.code === 0)) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    } else {
      ElMessage.error(res?.message || '获取传感器数据失败');
    }
  } catch (error) {
    console.error('获取传感器数据失败:', error);
    ElMessage.error('获取传感器数据失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = (params) => {
  queryParams.value = params;
  currentPage.value = 1;
  fetchSensorData();
};

// 处理重置
const handleReset = () => {
  queryParams.value = {};
  currentPage.value = 1;
  fetchSensorData();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchSensorData();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchSensorData();
};

// 表头样式
const headerCellStyle = {
  background: '#F4F4F4',
  fontFamily: 'PingFangSC, PingFang SC',
  fontWeight: '500',
  fontSize: '14px',
  color: '#282828',
  height: '64px'
};

// 斑马纹样式
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 === 1) {
    return 'zebra-row';
  }
  return '';
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行数据:', row);
};

// 操作按钮处理函数
const handleAdd = () => {
  dialogMode.value = 'add';
  currentRow.value = {};
  dialogVisible.value = true;
};

const handleImport = () => {
  ElMessage.info('功能开发中...');
};

const handleExport = () => {
  ElMessage.info('功能开发中...');
};

const handleEdit = (row) => {
  dialogMode.value = 'edit';
  currentRow.value = { ...row };
  dialogVisible.value = true;
};

const handleDetail = (row) => {
  dialogMode.value = 'view';
  currentRow.value = { ...row };
  dialogVisible.value = true;
};

const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除该设备吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const response = await deleteSensorDevice(row.id);
        if (response && (response.code === 200 || response.code === 0)) {
          ElMessage.success('删除成功');
          fetchSensorData();
        } else {
          ElMessage.error(response?.message || '删除失败');
        }
      } catch (error) {
        console.error('删除设备失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude, //经度
      latitude: row.latitude //维度
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 对话框成功提交回调
const handleDialogSuccess = () => {
  fetchSensorData();
};

// 计算表格高度
const calculateTableHeight = () => {
  nextTick(() => {
    if (tableContainerRef.value) {
      const containerHeight = tableContainerRef.value.offsetHeight;
      if (containerHeight > 0) {
        // 确保表格有足够的高度显示更多行
        tableHeight.value = Math.max(containerHeight, 500);
      } else {
        // 提高默认高度以确保显示更多行
        tableHeight.value = 600;
        setTimeout(calculateTableHeight, 100);
      }
    }
  });
};

// 设置滚动同步
const setupScrollSync = () => {
  nextTick(() => {
    const tableContainer = tableContainerRef.value;
    if (!tableContainer) return;
    
    const table = tableContainer.querySelector('.el-table');
    if (!table) return;
    
    const headerWrapper = table.querySelector('.el-table__header-wrapper');
    const bodyWrapper = table.querySelector('.el-table__body-wrapper');
    
    if (headerWrapper && bodyWrapper) {
      // 隐藏表头的水平滚动条
      headerWrapper.style.overflowX = 'hidden';
      
      // 同步滚动
      const syncScroll = () => {
        if (headerWrapper && bodyWrapper) {
          headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
        }
      };
      
      bodyWrapper.addEventListener('scroll', syncScroll);
      
      // 清理函数
      return () => {
        bodyWrapper.removeEventListener('scroll', syncScroll);
      };
    }
  });
};

// 窗口大小改变时重新计算表格高度
const handleResize = () => {
  calculateTableHeight();
};

onMounted(() => {
  fetchSensorData();
  calculateTableHeight();
  setupScrollSync();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.gas-monitor-sensor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  width: 80px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
}

/* 表格样式 - 自适应高度 */
.table-container {
  flex: 1;
  width: 100%;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.el-table) {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  overflow-x: hidden !important;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f5f5f5;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: #909399;
}

:deep(.el-table th) {
  background-color: #F4F4F4;
  height: 64px;
}

:deep(.el-table tr) {
  height: 48px;
}

:deep(.zebra-row) {
  background-color: #F8FAFD;
}

:deep(.el-table__row:hover) {
  background: #EEF5FF !important;
}

:deep(.el-table__row.selected-row) {
  background: #EEF5FF !important;
}

.operation-btns {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.operation-btn-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #0086FF;
  cursor: pointer;
}

.operation-divider {
  margin: 0 4px;
  color: #CED3DA;
}

/* 分页样式 - 固定在底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 0;
  padding-bottom: 8px;
  margin-top: 8px;
  min-height: 32px;
  flex-shrink: 0;
}

:deep(.el-pagination) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #222222;
  padding-right: 0;
}

:deep(.el-pagination .el-pager li) {
  width: 24px;
  height: 24px;
  background: rgba(255,255,255,0.99);
  border-radius: 2px;
  border: 1px solid #EEEEEE;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-pagination .el-pager li.is-active) {
  width: 24px;
  height: 24px;
  background: #0086FF;
  border-radius: 2px;
  color: #FFFFFF;
  border: none;
}

/* 确保操作列文字不换行 */
:deep(.cell) {
  white-space: nowrap;
}

/* 文本省略号样式 */
.text-ellipsis {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>