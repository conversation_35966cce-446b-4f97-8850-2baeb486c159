<template>
  <PanelBox title="高发报警设备" class="gas-monitoring-right-bottom-panel">
    <template #extra>
      <div class="tab-buttons">
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'week' }" 
          @click="changeTab('week')"
        >
          近1周
        </div>
        <div class="divider"></div>
        <div 
          class="tab-btn" 
          :class="{ active: activeTab === 'month' }" 
          @click="changeTab('month')"
        >
          近1月
        </div>
      </div>
    </template>
    <div class="panel-content">
      <!-- 使用滚动表格组件 -->
      <ScrollTable 
        :columns="tableColumns" 
        :data="deviceList" 
        :autoScroll="true" 
        :scrollSpeed="3000"
        :tableHeight="tableHeight" 
        :visibleRows="visibleRows" 
        @row-click="openDetailModal"
      >
        <!-- 自定义排名列 -->
        <template #ranking="{ row, index }">
          <div class="rank-box" :class="`rank-${row.ranking}`">
            {{ row.ranking }}
          </div>
        </template>
      </ScrollTable>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import ScrollTable from '@/components/screen/common/ScrollTable.vue'

import { getMonitorAnalysisDeviceHighFrequency } from '@/api/gas'

// 定义数据源
const activeTab = ref('week') // 默认选择近1周
const loading = ref(false)

// 表格列配置
const tableColumns = [
  { title: '排名', dataIndex: 'ranking', width: '10%', fontSize: '13px' },
  { title: '设备名称', dataIndex: 'deviceName', width: '60%', fontSize: '13px' },
  { title: '报警数量', dataIndex: 'alarmCount', width: '10%', fontSize: '13px' },
  { title: '处置完成率', dataIndex: 'resolveRate', width: '20%', fontSize: '13px' }
]

// 动态计算表格高度
const tableHeight = computed(() => {
  // 屏幕高度小于 1055px 时，表格高度为 225px 
  if (window.innerHeight < 1050) {
    return '220px'
  }
  if (window.innerHeight > 1051) {
    return '320px'
  }
  return '315px' // 可以根据不同分辨率动态调整
})

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight)
// 全屏状态响应式变量
const isFullscreen = ref(false)

// 检查全屏状态
const checkFullscreen = () => {
  const fullscreenElement = document.fullscreenElement || 
                           document.webkitFullscreenElement || 
                           document.mozFullScreenElement || 
                           document.msFullscreenElement
  return !!fullscreenElement
}

// 动态计算可见行数
const visibleRows = computed(() => {
  // 使用更低的阈值，确保在大屏幕或全屏时显示更多行
  const threshold = 1080
  return windowHeight.value >= threshold ? 8 : 6
})

// 窗口大小变化监听
const handleResize = () => {
  windowHeight.value = window.innerHeight
  isFullscreen.value = checkFullscreen()
}

// 全屏状态变化监听
const handleFullscreenChange = () => {
  isFullscreen.value = checkFullscreen()
  windowHeight.value = window.innerHeight
}

// 当前展示的设备列表数据
const deviceList = ref([])

// // 监听tab切换，更新数据
// watch(activeTab, () => {
//   fetchDeviceData()
// })

// 切换标签页
const changeTab = (tab) => {
  activeTab.value = tab
  fetchDeviceData()
}

// 格式化设备数据
const formatDeviceData = (records) => {
  return records.map((item, index) => ({
    ranking: index + 1,
    deviceName: item.deviceName,
    alarmCount: item.alarmCount,
    resolveRate: item.handleRate + '%',
    deviceId: item.deviceCode
  }))
}

// 获取设备数据
const fetchDeviceData = async () => {
  try {
    loading.value = true
    const response = await getMonitorAnalysisDeviceHighFrequency({
      dayIndex: activeTab.value === 'week' ? 7 : 30,
      pageNum: 1,
      pageSize: 10
    })
    if (response.code === 200 && response.data.records) {
      deviceList.value = formatDeviceData(response.data.records)
    }
  } catch (error) {
    console.error('获取高发报警设备数据失败', error)
  } finally {
    loading.value = false
  }
}

// 打开详情弹窗
const openDetailModal = (row) => {
  console.log('打开设备详情', row.deviceId)
  // 实际项目中可能需要打开设备详情弹窗
}


onMounted(() => {
  fetchDeviceData()
  // 初始化全屏状态
  isFullscreen.value = checkFullscreen()
  // 添加窗口resize事件监听
  window.addEventListener('resize', handleResize)
  // 添加全屏状态变化监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange)
  document.addEventListener('MSFullscreenChange', handleFullscreenChange)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
})
</script>

<style scoped>
.gas-monitoring-right-bottom-panel {
  height: 340px; /* 默认高度为340px */
}

.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px; 
  position: relative;
}

/* 标签按钮样式 */
.tab-buttons {
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
}

.tab-btn {
  cursor: pointer;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.3);
  line-height: 22px;
}

.tab-btn.active {
  color: rgba(255, 255, 255, 0.8);
}

/* 排名样式 */
.rank-box {
  width: 21px;
  height: 21px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
}

.rank-1 {
  background: rgba(242,140,45,0.2);
  border: 1px solid #F28C2D;
}

.rank-2 {
  background: rgba(212,212,212,0.2);
  border: 1px solid #D4D4D4;
}

.rank-3 {
  background: rgba(254,208,138,0.2);
  border: 1px solid #FED08A;
}

.rank-4, .rank-5, .rank-6, .rank-7, .rank-8, .rank-9, .rank-10 {
  background: rgba(0,170,255,0.2);
  border: 1px solid #00AAFF;
  opacity: 0.7;
}

/* 点击行样式 */
:deep(.scroll-table tr) {
  height: 30px;
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.scroll-table tr:hover) {
  background-color: rgba(0, 163, 255, 0.2) !important;
}


/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .gas-monitoring-right-bottom-panel {
    height: 340px;
  }
}

@media screen and (max-width: 1919px) {
  .gas-monitoring-right-bottom-panel {
    height: 320px;
  }
}

@media screen and (min-width: 2561px) {
  .gas-monitoring-right-bottom-panel {
    height: 380px;
  }
} 

/* 940px-1055px高度的屏幕特别优化 */
@media screen and (min-height: 940px) and (max-height: 1055px) {
  .gas-monitoring-right-bottom-panel {
    height: 320px;
  }
  
  .panel-content {
    padding: 5px 10px;
    gap: 0px;
  }
  
  .tab-buttons {
    margin-right: 25px;
  }
  
  .tab-btn {
    font-size: 14px;
    line-height: 15px;
  }
  
  .divider {
    height: 14px;
    margin: 0 8px;
  }
  
  .rank-box {
    width: 19px;
    height: 19px;
    font-size: 13px;
  }
}
</style>